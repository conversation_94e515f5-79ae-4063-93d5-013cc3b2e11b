import {initializeApp} from "firebase/app";
import {getMessaging, getToken, onMessage} from "firebase/messaging";
import {getVariable, pushNotificationQueue, QueueType, setVariable} from "@/services/indexedDb.ts";
import securityApi from "@/api/securityApi.ts";

const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID,
};

const messagingVapidKey = import.meta.env.VITE_FIREBASE_MESSAGING_VAPID_KEY;

export function initFirebaseApp() {
  const app = initializeApp(firebaseConfig);
  const messaging = getMessaging(app);

  getToken(messaging, {vapidKey: messagingVapidKey}).then(async token => {
    console.log("FcmToken:", token);
    await securityApi.mountEvent(navigator, token);
  });

  onMessage(messaging, async (payload) => {
    console.log('[ForeStage] Message received. ', payload);
    if (!payload.data) return;
    /** 這段是論文測試用的，可以刪除 */
    if (!payload.data.Mode) {
      let paperTemp: any[] = await getVariable('paperTemp');
      paperTemp.push({
        ...payload.data,
        receiveTime: Date.now(),
      });
      await setVariable('paperTemp', paperTemp);
      return;
    }
    /** 這段是論文測試用的，可以刪除 */
    const item: QueueType = {
      createdAt: new Date(),
      title: payload.data.Title ?? "未知的標題",
      body: payload.data.Body ?? "未知的內容",
      link: payload.data.Link ?? "",
    };
    await pushNotificationQueue(item);
    const notification = new Notification(item.title, {
      body: item.body,
      icon: "/homeeasy_icon.svg",
    });
    notification.onclick = (event) => {
      event.preventDefault(); // Prevent the browser from focusing the Notification's tab
      window.open(item.link);
    };
  });
}
