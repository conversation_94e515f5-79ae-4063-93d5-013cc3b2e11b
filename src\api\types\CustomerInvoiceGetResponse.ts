import {ResponseBase} from "@/api/types/ResponseBase.ts";
import {OrderType} from "@/types/OrderType.ts";

export interface CustomerInvoiceGetResponse extends ResponseBase {
  result: CustomerInvoiceGetItem[];
}

export interface CustomerInvoiceGetItem {
  invoiceId: string;
  createTime: string;
  refreshTime: string;
  orderCreateTime: string;
  orderType: OrderType;
  orderId: string;
  customerId: string;
  customerName: string;
  address: Address;
  amount: number;
  designerId: string;
  designerName: string;
  application: Application;
}

export interface Address {
  fullName: string;
  sampleName: string;
  location: {
    lat: number;
    lng: number;
  };
}

export interface Application {
  taxId: string;
  title: string;
  note: string;
}

export interface IssueResult {
  createTime: Date;
  fileUrl: string;
  amount: number;
}
