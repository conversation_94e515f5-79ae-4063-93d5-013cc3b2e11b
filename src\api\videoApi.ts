import {postApiByToken} from "@/api/baseApi";
import {VideoGetListResponse, VideoStatus} from "@/api/types/VideoGetListResponse.ts";
import {VideoType, VideoTypeReq} from "@/types/VideoType.ts";
import {VideoGetOneResponse} from "@/api/types/VideoGetOneResponse.ts";

export default {
  getList: async (skip: number, limit: number, search: string, type: VideoTypeReq) =>
    await postApiByToken<VideoGetListResponse>(
      'Ms/Video/GetList',
      {skip, limit, search, type}),

  getOne: async (videoId: string) =>
    await postApiByToken<VideoGetOneResponse>(
      'Ms/Video/GetOne',
      {videoId}),

  update: async (youtubeId: string, type: VideoType) =>
    await postApiByToken<VideoGetOneResponse>(
      'Ms/Video/Update',
      {youtubeId, type}),

  editInfo: async (videoId: string, title: string, description: string, type: VideoType) =>
    await postApiByToken<VideoGetOneResponse>(
      'Ms/Video/EditInfo',
      {videoId, title, description, type}),

  editStatus: async (videoId: string, editStatus: VideoStatus) =>
    await postApiByToken<VideoGetOneResponse>(
      'Ms/Video/EditStatus',
      {videoId, editStatus}),
}
