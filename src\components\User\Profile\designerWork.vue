<template>
  <a-popover title="設計師類型">
    <template #content>
      服務項目：
      <div style="margin-bottom: 8px">
        <a-checkbox
          v-model:checked="(model as Work).type.surveyor"
          @change="onCheckBoxChange(DesignerWorkType.Surveyor)"
        >
          丈量師
        </a-checkbox>
        <a-checkbox
          v-model:checked="(model as Work).type.designer"
          @change="onCheckBoxChange(DesignerWorkType.Designer)"
          >設計師</a-checkbox
        >
        <a-checkbox
          v-model:checked="(model as Work).type.decorator"
          @change="onCheckBoxChange(DesignerWorkType.Decorator)"
          >裝修師</a-checkbox
        >
      </div>
      服務地區：
      <div style="margin-bottom: 8px; max-width: 300px">
        {{ regionStr }}
      </div>
    </template>
    <a>
      <a-tag color="blue" style="text-align: center"> 設計師<br /> </a-tag>
    </a>
  </a-popover>
</template>
<script lang="ts" setup>
import { Work } from "@/api/types/UserAuditGetOneResponse.ts";
import userApi from "@/api/userApi.ts";
import { notifyPush, notifyType } from "@/services/notify.ts";
import { computed } from "vue";
import { RegionEnum } from "@/types/RegionEnum.ts";
import { DesignerWorkType } from "@/types/DesignerWorkType.ts";

const props = defineProps(["userId"]);
const model = defineModel<Work>();
const regionStr = computed(() => {
  if (!model.value?.region) return "錯誤";
  if (model.value?.region.length === 0) return "無";
  return model.value?.region.map((value) => RegionEnum[value]).join("、");
});

async function onCheckBoxChange(workType: DesignerWorkType) {
  if (!model.value?.type) return;

  let currentValue: boolean;
  let successMessage: string;

  // 根據工作類型取得對應的數值和成功訊息
  switch (workType) {
    case DesignerWorkType.Surveyor:
      currentValue = model.value.type.surveyor;
      successMessage = "丈量師設定修改成功";
      break;
    case DesignerWorkType.Designer:
      currentValue = model.value.type.designer;
      successMessage = "設計師設定修改成功";
      break;
    case DesignerWorkType.Decorator:
      currentValue = model.value.type.decorator;
      successMessage = "裝修師設定修改成功";
      break;
    default:
      return;
  }

  const response = await userApi.profile.updateWork(
    props.userId,
    currentValue,
    workType
  );

  if (response.status !== 0) {
    // 如果API調用失敗，還原checkbox狀態
    switch (workType) {
      case DesignerWorkType.Surveyor:
        model.value.type.surveyor = !currentValue;
        break;
      case DesignerWorkType.Designer:
        model.value.type.designer = !currentValue;
        break;
      case DesignerWorkType.Decorator:
        model.value.type.decorator = !currentValue;
        break;
    }
  } else {
    notifyPush({
      type: notifyType.success,
      message: successMessage,
    });
  }
}
</script>
<style scoped></style>
