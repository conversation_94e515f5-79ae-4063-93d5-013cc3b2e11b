<template>
  <h2>網頁權限</h2>
  <a-descriptions :column="1" bordered size="small">
    <a-descriptions-item label="通知權限">
      <a-button v-if="notificationPermission === 'granted'" @click="pushNotification('此為測試訊息')">
        已允許 (點擊測試)
      </a-button>
      <a-button v-else-if="notificationPermission === 'default'" type="primary" @click="NotificationPermission">
        未允許 (前往設定)
      </a-button>
      <a-button v-else disabled style="color: red">已封鎖 (請至瀏覽器設定)</a-button>
    </a-descriptions-item>
  </a-descriptions>
</template>
<script lang="ts" setup>
import {ref} from "vue";
import {pushNotification, requestNotificationPermission} from "@/services/notification.ts";

const notificationPermission = ref<NotificationPermission>(Notification.permission);

async function NotificationPermission() {
  if (Notification.permission !== "default") {
    notificationPermission.value = Notification.permission;
    return;
  }
  notificationPermission.value = await requestNotificationPermission();
}
</script>
<style scoped>

</style>
