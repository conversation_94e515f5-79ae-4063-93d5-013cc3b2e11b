<template>
  <a-card :title="title" size="small">
    <div style="display: flex">
      <a-image
        v-if="props.avatar.url !== ''"
        :src="props.avatar.url"
        height="100px"
        width="100px"
      />
      <p v-else style="width: 100px">圖片未上傳</p>
      <div style="margin-left: 16px">
        <div style="display: flex">
          <p>
            修改資料時間：{{ new Date(props.refreshTime).toLocaleString() }}
          </p>
        </div>
        <div style="display: flex">
          <p>身分驗證狀態：</p>
          <VerifyTaiwanIdButtonModel
            v-model="props.taiwanId"
            :userId="props.userId"
            :userType="props.userType"
          />
        </div>
        <div v-if="props.userType === UserType.Designer" style="display: flex">
          <p>公司驗證狀態：</p>
          <VerifyCompanyButtonModel
            v-model="props.designerInfo.company"
            :userId="props.userId"
          />
        </div>
        <div style="display: flex">
          <p>信箱驗證狀態：</p>
          <p v-if="props.email.address !== ''">{{ props.email.address }}</p>
          <p v-if="props.email.isVerify">(已認證)</p>
          <p v-else>(未認證)</p>
        </div>
      </div>
    </div>
    <div v-if="props.userType === UserType.Designer">
      <div>
        <a-checkbox
          v-model:checked="props.designerInfo.work.type.surveyor"
          @change="onCheckBoxChange(DesignerWorkType.Surveyor)"
        >
          丈量師
        </a-checkbox>
        <a-checkbox
          v-model:checked="props.designerInfo.work.type.designer"
          @change="onCheckBoxChange(DesignerWorkType.Designer)"
          >設計師</a-checkbox
        >
        <a-checkbox
          v-model:checked="props.designerInfo.work.type.decorator"
          @change="onCheckBoxChange(DesignerWorkType.Decorator)"
          >裝修師</a-checkbox
        >
      </div>
    </div>
  </a-card>
</template>
<script lang="ts" setup>
import { UserAuditGetItem } from "@/api/types/UserAuditGetOneResponse.ts";
import VerifyTaiwanIdButtonModel from "@/components/User/Audit/verifyTaiwanIdButtonModel.vue";
import VerifyCompanyButtonModel from "@/components/User/Audit/verifyCompanyButtonModel.vue";
import userApi from "@/api/userApi.ts";
import { UserType } from "@/types/UserType.ts";
import { DesignerWorkType } from "@/types/DesignerWorkType.ts";

const props = defineProps<UserAuditGetItem>();
const emit = defineEmits<{
  refresh: [userId: string, loading: "start" | "end"];
}>();
const title = `[${props.userType === UserType.Customer ? "客戶" : "設計師"}] ${
  props.taiwanId.username
}(${props.phone}) 更新了個人資訊 (用戶ID=${props.userId})`;

async function onCheckBoxChange(workType: DesignerWorkType) {
  emit("refresh", props.userId, "start");

  let currentValue: boolean;

  // 根據工作類型取得對應的數值
  switch (workType) {
    case DesignerWorkType.Surveyor:
      currentValue = props.designerInfo.work.type.surveyor;
      break;
    case DesignerWorkType.Designer:
      currentValue = props.designerInfo.work.type.designer;
      break;
    case DesignerWorkType.Decorator:
      currentValue = props.designerInfo.work.type.decorator;
      break;
    default:
      emit("refresh", props.userId, "end");
      return;
  }

  const response = await userApi.profile.updateWork(
    props.userId,
    currentValue,
    workType
  );

  if (response.status !== 0) {
    // 如果API調用失敗，還原checkbox狀態
    switch (workType) {
      case DesignerWorkType.Surveyor:
        props.designerInfo.work.type.surveyor = !currentValue;
        break;
      case DesignerWorkType.Designer:
        props.designerInfo.work.type.designer = !currentValue;
        break;
      case DesignerWorkType.Decorator:
        props.designerInfo.work.type.decorator = !currentValue;
        break;
    }
  }

  emit("refresh", props.userId, "end");
}
</script>
<style scoped></style>
