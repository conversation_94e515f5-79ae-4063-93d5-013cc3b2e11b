<template>
  <a-spin :spinning="spinning" tip="Loading...">
    <a-card>
      <h1>待開立發票給客戶 ({{ listData.length === 0 ? "尚無資料" : `共${listData.length}筆` }})：</h1>
      <a-card v-for="item in listData"
              :title="item.title" size="small">
        <div style="padding: 16px">
          <div style="display: flex; flex-wrap: wrap;">
            <a-card style="margin: 0 16px 16px 0" title="訂單資訊">
              <p>訂單ID： {{ item.orderId }}</p>
              <p>訂單地址： {{ item.address.fullName }}</p>
              <p>訂單建立時間： {{ new Date(item.orderCreateTime).toLocaleString() }}</p>
              <p>客戶名稱： {{ item.customerName }}</p>
              <p>設計師名稱： {{ item.designerName }}</p>
              <p>發票金額： {{ item.amount }}元</p>
              <p>發票抬頭： {{ item.application.title }}</p>
              <p>發票統編： {{ item.application.taxId }}</p>
              <p>客戶備註： {{ item.application.note }}</p>
            </a-card>
            <a-card style="margin: 0 16px 16px 0" title="上傳開立發票">
              <UploadTool v-model:files="item.files" :show-upload-list="false" s3-path="invoice"/>
              <a-button :disabled="item.isFinished" style="margin: 8px 8px 0 8px" type="primary"
                        @click="verifyHandle(item.invoiceId, item.files, item.amount)">
                完成開立發票
              </a-button>
              <div v-for="(file, index) in item.files" style="margin: 16px 0 0 0">
                <div>
                  {{ index + 1 }}. <a :href="file.url" target="_blank">{{ file.name }}</a>
                </div>
                <a-date-picker placeholder="開立發票時間" show-time style="margin: 0 8px 0 8px"
                               @change="(time: any) => datePickerOk(time, file)"/>
                <a-input-number v-model:value.lazy="file.amount" addon-after="元"
                                placeholder="開立發票金額"></a-input-number>
              </div>
              <br>
            </a-card>
          </div>
        </div>
      </a-card>
    </a-card>
  </a-spin>
</template>
<script lang="ts" setup>
import {notifyPush, notifyPushError, notifyType} from "@/services/notify.ts";
import {ref} from "vue";
import orderContractApi from "@/api/orderContractApi.ts";
import UploadTool from "@/components/Order/Audit/UploadTool/UploadTool.vue";
import {FileListType} from "@/components/Order/Audit/UploadTool/UploadToolType.ts";
import {
  CustomerInvoiceGetItem,
  CustomerInvoiceGetResponse,
  IssueResult
} from "@/api/types/CustomerInvoiceGetResponse.ts";

const spinning = ref<boolean>(true);
const listData = ref<listDataType[]>([]);

refreshList();

async function refreshList(res: CustomerInvoiceGetResponse | null = null) {
  if (!res) res = await orderContractApi.invoiceGet();
  if (!res || res.status !== 0) notifyPushError();
  else {
    listData.value = res.result.map(item => {
      const title = `【Step${item.orderType}】需開立給${item.customerName}${item.amount}元發票 (發票ID=${item.invoiceId})`;
      return {
        ...item,
        title,
        files: [],
        uploading: false,
        isFinished: false,
      };
    });
    spinning.value = false;
  }
}

const verifyHandle = async (invoiceId: string, files: FileListWithTimeType[], totalAmount: number) => {
  console.log('verifyHandle:', invoiceId);
  console.dir(files);
  if (files.length === 0)
    throw notifyPushError("請上傳檔案");
  if (files.findIndex(file => !file.time) !== -1)
    throw notifyPushError("請選擇時間");
  if (files.findIndex(file => !file.amount) !== -1)
    throw notifyPushError("請填寫金額");
  if (files.reduce((acc, file) => acc + (file.amount ?? 0), 0) !== totalAmount)
    throw notifyPushError("金額加總不等於發票金額");
  const issueResults: IssueResult[] = files.map(file => ({
    createTime: file.time ?? new Date(0),
    fileUrl: file.url,
    amount: file.amount ?? -1,
  }));
  const response = await orderContractApi.invoiceSet(invoiceId, issueResults);
  if (!response || response.status !== 0) throw notifyPushError();
  notifyPush({
    type: notifyType.success,
    message: `操作成功`,
  });
  await refreshList(response);
};

const datePickerOk = (time: any, file: FileListWithTimeType) => {
  file.time = !time ? undefined : (time.$d as Date);
};

interface listDataType extends CustomerInvoiceGetItem {
  title: string;
  files: FileListWithTimeType[];
  uploading: boolean;
  isFinished: boolean;
}

interface FileListWithTimeType extends FileListType {
  time?: Date;
  amount?: number;
}
</script>
<style scoped>
</style>
