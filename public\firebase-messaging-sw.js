importScripts('https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js');
importScripts('https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js');
importScripts('https://unpkg.com/idb@5.0.4/build/iife/index-min.js');

console.log('firebase-messaging-sw.js loaded');

// Initialize Firebase
const firebaseConfig = {
    apiKey: "AIzaSyAH6NYEcNF_ii3rzgmwZWgP_cdycZYc-Cw",
    authDomain: "homeeasy-dev.firebaseapp.com",
    projectId: "homeeasy-dev",
    storageBucket: "homeeasy-dev.appspot.com",
    messagingSenderId: "725275941001",
    appId: "1:725275941001:web:a0d058010dc8f1ffe0ce58",
    measurementId: "G-GE1THGYKJP",
};
firebase.initializeApp(firebaseConfig);
const messaging = firebase.messaging();

// Initialize IndexedDB
const dbName = 'homeeasy-database';
const tableName = 'notification';
const dbPromise = idb.openDB(dbName, 1, {
    upgrade(db) {
        db.createObjectStore(tableName);
    },
});

async function setVariable(name, value) {
    const db = await dbPromise;
    await db.put(tableName, value, name);
}

async function getVariable(name) {
    const db = await dbPromise;
    try {
        return await db.get(tableName, name);
    } catch {
        return null;
    }
}

messaging.onBackgroundMessage(async (payload) => {
    console.log('[BackStage] Message received. ', payload);

    if (!payload.data.Mode) return;

    const queue = await getVariable('queue') || [];
    const item = {
        createdAt: new Date(),
        title: payload.data.Title ?? "未知的標題",
        body: payload.data.Body ?? "未知的內容",
        link: payload.data.Link ?? "",
    };
    queue.push(item);
    await setVariable('queue', queue);

    await self.registration.showNotification(item.title, {
        body: item.body,
        icon: "homeeasy_icon.svg",
        data: {link: item.link},
    });
});

// 添加 notificationclick 事件處理邏輯
self.addEventListener('notificationclick', function (event) {
    event.notification.close(); // 關閉通知
    const link = event.notification.data.link; // 獲取通知中的鏈接數據

    event.waitUntil(
        clients.matchAll({type: 'window', includeUncontrolled: true}).then(windowClients => {
            // 檢查是否有打開的窗口
            for (let i = 0; i < windowClients.length; i++) {
                const client = windowClients[i];
                if (client.url === link && 'focus' in client) {
                    return client.focus();
                }
            }
            // 如果沒有打開的窗口，則打開新窗口
            if (clients.openWindow) {
                return clients.openWindow(link);
            }
        })
    );
});
