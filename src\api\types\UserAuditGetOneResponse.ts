import {ResponseBase} from "@/api/types/ResponseBase.ts";
import {Avatar, Email} from "@/api/types/UserProfileListResponse.ts";
import {VerifyStatus} from "@/types/VerifyStatus.ts";
import {UserType} from "@/types/UserType.ts";
import {Gender} from "@/types/Gender.ts";
import {RegionEnum} from "@/types/RegionEnum.ts";

export interface UserAuditGetOneResponse extends ResponseBase {
  result: UserAuditGetItem;
}

export interface UserAuditGetItem {
  refreshTime: Date;
  userType: UserType;
  userId: string;
  phone: string;
  avatar: Avatar;
  taiwanId: UserAuditTaiwanId;
  email: Email;
  designerInfo: UserAuditGetDesignerItem;
}

export interface UserAuditGetDesignerItem {
  verifyStatus: VerifyStatus;
  company: Company;
  work: Work;
}

export interface UserAuditTaiwanId {
  updateTime: string;
  verifyStatus: VerifyStatus;
  username: string;
  gender: Gender;
  idNumber: string;
  frontSideUrl: string;
  backSideUrl: string;
}

export interface Company {
  updateTime: Date;
  verifyStatus: VerifyStatus;
  companyName: string;
  unifiedBusinessNumber: string;
  companyDocumentUrls: string[];
  description: string;
  logo: string;
  serviceTime: string;
  address: string;
}

export interface Work {
  updateTime: Date;
  type: {
    surveyor: boolean;
    designer: boolean;
    decorator: boolean;
  };
  region: RegionEnum[];
}
