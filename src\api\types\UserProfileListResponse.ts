import { ResponseBase } from "@/api/types/ResponseBase.ts";
import { UserType } from "@/types/UserType.ts";
import { Gender } from "@/types/Gender.ts";
import { VerifyStatus } from "@/types/VerifyStatus.ts";
import { Company, Work } from "@/api/types/UserAuditGetOneResponse.ts";

export interface UserProfileListResponse extends ResponseBase {
  result: UserProfileListItem[];
}

export interface UserProfileListItem {
  index: number;
  createTime: string;
  refreshTime: string;
  isDeleted: boolean;
  userType: UserType;
  userId: string;
  username: string;
  phone: string;
  avatar: Avatar;
  email: Email;
  gender: Gender;
  taiwanId: TaiwanId;
  company?: Company;
  bankAccount?: BankAccount;
  work?: Work;
  authToken: string;
  deviceInfo: DeviceInfo;
  verifyStatus: VerifyStatus;
  isOrderEnabled: boolean;
}

export interface Avatar {
  updateTime: string;
  url: string;
}

export interface TaiwanId {
  updateTime: string;
  verifyStatus: VerifyStatus;
  idNumber: string;
  frontSideUrl: string;
  backSideUrl: string;
}

export interface Email {
  updateTime: string;
  address: string;
  isVerify: boolean;
}

export interface BankAccount {
  isSet: boolean;
  updateTime: string;
  accountName: string;
  bankCode: string;
  accountNumber: string;
}

export interface DeviceInfo {
  deviceType: DeviceTypeEnum;
  deviceBrand: string;
  deviceModel: string;
  deviceVersion: string;
  deviceRegion: string;
  appVersion: string;
  ipAddress: string;
}

export enum DeviceTypeEnum {
  Unknown = 0,
  Android = 1,
  IOS = 2,
  Web = 3,
}
