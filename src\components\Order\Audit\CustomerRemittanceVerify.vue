<template>
  <a-spin :spinning="spinning" tip="Loading...">
    <a-card>
      <h1>客戶匯款審核 ({{ listData.length === 0 ? "尚無資料" : `共${listData.length}筆` }})：</h1>
      <a-card v-for="item in listData"
              :title="item.title" size="small">
        <div style="display: flex;">
          <a-space>
            <a-image v-for="img in item.proofImageUrls" :src="img" height="100px" width="100px"/>
          </a-space>
          <div style="padding: 16px">
            <p>匯款時間： {{ new Date(item.remittanceTime).toLocaleString() }}</p>
            <p>匯款金額： {{ item.amount }}元</p>
            <a-space>
              <a-button danger @click="verifyHandle(item.remittanceId, false)">
                審核失敗
              </a-button>
              <a-button primary @click="verifyHandle(item.remittanceId, true)">
                審核通過
              </a-button>
            </a-space>
          </div>
        </div>
      </a-card>
    </a-card>
  </a-spin>
</template>
<script lang="ts" setup>
import {notifyPush, notifyPushError, notifyType} from "@/services/notify.ts";
import {ref} from "vue";
import {
  CustomerRemittanceGetItem,
  CustomerRemittanceGetResponse
} from "@/api/types/CustomerRemittanceGetResponse.ts";
import orderContractApi from "@/api/orderContractApi.ts";

const spinning = ref<boolean>(true);
const listData = ref<listDataType[]>([]);

refreshList();

async function refreshList(res: CustomerRemittanceGetResponse | null = null) {
  if (!res) res = await orderContractApi.remittanceCustomerGet();
  if (!res || res.status !== 0) notifyPushError();
  else {
    listData.value = res.result.map(item => {
      const title = `【Step${item.orderType}】需審核${item.customerName}的匯款資訊 (訂單ID=${item.orderId}})`;
      return {
        ...item,
        title,
      };
    });
    spinning.value = false;
  }
}

const verifyHandle = async (remittanceId: string, isVerifyPass: boolean) => {
  const response = await orderContractApi.remittanceCustomerVerify(remittanceId, isVerifyPass);
  if (!response || response.status !== 0) notifyPushError();
  else {
    notifyPush({
      type: notifyType.success,
      message: `審核(${isVerifyPass ? "通過" : "失敗"})成功`,
    });
    await refreshList(response);
  }
};

interface listDataType extends CustomerRemittanceGetItem {
  title: string;
}
</script>
<style scoped>
</style>
