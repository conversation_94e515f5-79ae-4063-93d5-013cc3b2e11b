{"name": "homeeasy-manage-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"debug:test": "vite --mode test", "debug:dev": "vite --mode development", "debug:prod": "vite --mode production", "build:test": "vue-tsc && vite build --mode test", "build:dev": "vue-tsc && vite build --mode development", "build:prod": "vue-tsc && vite build --mode production", "preview": "vite preview", "start:test": "yarn build:test && yarn preview", "start:dev": "yarn build:dev && yarn preview", "start:prod": "yarn build:prod && yarn preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@aws-sdk/client-s3": "^3.569.0", "@microsoft/signalr": "^8.0.0", "@types/uuid": "^9.0.8", "@vueuse/core": "^10.8.0", "ant-design-vue": "4.1.2", "axios": "^1.6.2", "buffer": "^6.0.3", "crypto-js": "^4.2.0", "firebase": "^10.8.0", "idb": "^8.0.0", "moment": "^2.30.1", "pinia": "^2.1.7", "shx": "^0.3.4", "uuid": "^9.0.1", "vue": "^3.4.19", "vue-router": "4", "vue3-cookies": "^1.0.6"}, "devDependencies": {"@types/crypto-js": "^4.2.1", "@vitejs/plugin-vue": "^4.5.0", "typescript": "^5.2.2", "vite": "^5.1.4", "vue-tsc": "^1.8.22"}}