<template>
  <div :style="{ height: height + 'px', width: width + 'px' }" class="video-responsive">
    <iframe
        :height="height"
        :src="videoUrl"
        :width="width"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowfullscreen
        frameborder="0"
        style="border-radius: 10px 10px 0 0"
        title="YouTube video player"
    ></iframe>
  </div>
</template>

<script lang="ts">
import {computed, defineComponent} from 'vue';

export default defineComponent({
  name: 'YoutubeEmbed',
  props: {
    videoId: {
      type: String,
      required: true,
    },
    width: {
      type: Number,
      default: 560, // 默認寬度
    },
    height: {
      type: Number,
      default: 315, // 默認高度
    },
  },
  setup(props) {
    const videoUrl = computed(() => `https://www.youtube.com/embed/${props.videoId}`);
    return {
      videoUrl,
    };
  },
});
</script>
