# HomeEasy 後台管理系統

- 正式版：https://manage.homeeasy.app
- 測試版：https://manage-dev.homeeasy.app

## 開發環境設定

以下為當時開發的環境，但不限於此。

- Node v20.12.2
- Docker v20.10.23

### 執行

- 執行開發環境 (讀取 [.env.test](.env.test))
  ```sh
  yarn debug:test
  ```
- 執行開發環境 (讀取 [.env.develop](.env.development))
  ```sh
  yarn debug:dev
  ```
- 執行開發環境 (讀取 [.env.production](.env.production))
  ```sh
  yarn debug:prod
  ```

### 部署

- 正式版：[HEZ-DesignerWeb-AWS](https://ci2.bluenet-ride.com/view/HomeEasy/job/HEZ-DesignerWeb-AWS/)
- 測試版：[HEZ-DesignerWeb-DEV-AWS](https://ci2.bluenet-ride.com/view/HomeEasy/job/HEZ-DesignerWeb-DEV-AWS/)

部署前務必要先執行以下指令，確保編譯正確後，再 Commit Push 上 Github 進行自動部署。

```sh
yarn build:test
```
