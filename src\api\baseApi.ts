import axios, {AxiosRequestConfig} from "axios";
import {userStatusStore} from "@/stores/userStatusStore.ts";
import {notifyPush, notifyPushError, notifyType} from "@/services/notify.ts";

const baseURL = import.meta.env.VITE_API_URL;

export async function postApi<T>(url: string, body: any = {}, token?: string): Promise<T> {
  const config: AxiosRequestConfig = {
    method: 'post',
    url: `${baseURL}/Api/${url}`,
    headers: {
      Authorization: token,
      'Content-Type': 'application/json'
    },
    data: body
  };
  try {
    const {data} = await axios.request(config);
    console.log({time: new Date().toLocaleString(), api: url, request: body, response: data});
    return data;
  } catch (e) {
    console.error({time: new Date().toLocaleString(), api: url, request: body, error: e});
    notifyPushError();
    throw e;
  }
}

export async function postApiByToken<T>(url: string, body: any = {}): Promise<T> {
  const userStatus = userStatusStore();
  const token = userStatus.token;
  let config = {
    method: 'post',
    url: `${baseURL}/Api/${url}`,
    headers: {
      'Authorization': token ?? '',
      'Content-Type': 'application/json'
    },
    data: body
  };
  try {
    const {data} = await axios.request(config);
    console.log({time: new Date().toLocaleString(), api: url, token, request: body, response: data});
    if (data.status === 1) {
      userStatus.setLogOut();
      notifyPush({
        key: 'loginExpired',
        type: notifyType.error,
        message: '登入逾期，請重新登入',
      });
    }
    return data;
  } catch (e) {
    console.error({time: new Date().toLocaleString(), api: url, token, request: body, error: e});
    notifyPushError();
    throw e;
  }
}
