import {defineStore} from "pinia";
import {useStorage} from "@vueuse/core";
import {UserProfileListItem} from "@/api/types/UserProfileListResponse.ts";

export const userProfileStore = defineStore('pageUserProfile', {
  state: () => ({
    data: useStorage<UserProfileListItem[]>(
      "userProfileData",
      [],
    ),
    isFixed: useStorage<boolean>(
      "userProfileIsFixed",
      false,
    ),
  }),
  getters: {
    count: (state) => state.data.length,
  },
  actions: {
    update(data: UserProfileListItem[]) {
      this.data = data;
    },
    delete() {
      this.data = [];
    }
  },
});
