import {ResponseBase} from "@/api/types/ResponseBase.ts";

export interface ChatMessageSendResponse extends ResponseBase {
  message: MessageData;
}

export interface MessageData {
  messageId: string;
  createTime: string;
  refreshTime: string;
  status: ChatMessageStatus;
  roomId: string;
  userId: string;
  message: messageContent;
}

export enum ChatMessageStatus {
  Send = 0,
  Unsend = 1,
}

export interface messageContent {
  type: MessageContentType;
  sticker?: string;
  text?: string;
  files?: {
    type: FileType;
    name: string;
    url: string;
  }[];
  meet?: {
    type: MeetType;
  };
}

export enum MessageContentType {
  None = 0,
  Sticker = 1,
  Text = 2,
  FilesAndText = 3,
  Meet = 4,
}

export enum MeetType {
  SingleEnd = 11,
  SingleReject = 12,
  SingleNoResponse = 13,
  ConferenceStart = 20,
  ConferenceEnd = 21,
}

export enum FileType {
  Image = 1,
  Video = 2,
  File = 3,
}
