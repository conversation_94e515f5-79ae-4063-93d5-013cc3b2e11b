import {createRouter, createWebHistory} from 'vue-router'
import {routerMap} from "@/router/config.ts";
import {RouteRecordBody, RouterMapType} from "@/router/types.ts";

const routerMapTransform = routerMap
  .flatMap((router: RouterMapType) => router.type === 'body' ? router.body : router)
  .map((body: RouteRecordBody) => {
    if ("component" in body)
      return {
        name: body.key,
        path: body.path,
        meta: {title: body.name},
        component: body.component as never,
      }
    else if (body.redirect)
      return {
        name: body.key,
        path: body.path,
        redirect: body.redirect as string,
      }
    else
      return {
        name: body.key,
        path: body.path,
        redirect: "/404",
      }
  });

const routes = [
  ...routerMapTransform,
  {
    name: "notFound",
    path: "/not-found",
    component: () => import('../views/NotFoundView.vue'),
  },
  {
    path: "/:pathMatch(.*)",
    redirect: "/not-found",
  },
];

export default createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})
