import {PutObjectCommand, S3Client} from "@aws-sdk/client-s3";
import {Buffer} from 'buffer';

const isProd = import.meta.env.VITE_ENV === 'production';
const s3Client = new S3Client({
  region: import.meta.env.VITE_AWS_REGION,
  credentials: {
    accessKeyId: import.meta.env.VITE_AWS_ACCESS_KEY,
    secretAccessKey: import.meta.env.VITE_AWS_SECRET_KEY,
  },
});

export async function uploadImageToS3ByBase64(base64: string, key: string, type: string): Promise<string> {
  // console.log('uploadFileToS3ByBase64', key, type);
  key = `${isProd ? 'prod' : 'dev'}/${key}`;
  const result = await s3Client.send(new PutObjectCommand({
    Bucket: 'hezbox',
    Key: key,
    Body: Buffer.from(base64.replace(/^data:image\/\w+;base64,/, ""), 'base64'),
    ContentEncoding: 'base64',
    ACL: "public-read",
    ContentType: type,
  }));
  if (result.$metadata.httpStatusCode !== 200)
    throw new Error('Failed to upload file to S3');
  return `https://hezbox.s3.ap-northeast-1.amazonaws.com/${key}`
}
