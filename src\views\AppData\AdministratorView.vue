<template>
  <h2>新增管理員：</h2>
  <a-descriptions :column="2" bordered size="small" style="margin-bottom: 16px">
    <a-descriptions-item label="UserId">
      <a-input v-model:value="userIdInput"/>
    </a-descriptions-item>
    <a-descriptions-item>
      <a-button type="primary" @click="addUser">新增管理員</a-button>
    </a-descriptions-item>
    <a-descriptions-item label="備註">
      <a-input v-model:value="descriptionInput"/>
    </a-descriptions-item>
  </a-descriptions>
  <h2 style="margin-top: 32px">管理員列表：</h2>
  <a-list :data-source="docs" bordered item-layout="horizontal" size="small">
    <template #renderItem="{ item }">
      <a-list-item>
        <DeleteOutlined style="color: red; margin-right: 8px;" @click="deleteUser(item.userId)"/>
        <a-tag>更新時間：{{ new Date(item.refreshTime).toLocaleString() }}</a-tag>
        用戶名稱：{{ item.userName }}　　備註：{{ item.description }}　　UserId：{{ item.userId }}
      </a-list-item>
    </template>
  </a-list>
</template>
<script lang="ts" setup>
import {DeleteOutlined, ExclamationCircleOutlined} from '@ant-design/icons-vue';
import {createVNode, ref} from "vue";
import developmentApi from "@/api/developmentApi.ts";
import {notifyPushError} from "@/services/notify.ts";
import {Modal} from "ant-design-vue";
import {AdministratorItem} from "@/api/types/DevAdministratorGetResponse.ts";

const docs = ref<AdministratorItem[]>([]);
const userIdInput = ref<string>('');
const descriptionInput = ref<string>('');

developmentApi.Administrator.get().then(res => {
  if (res.status !== 0) throw notifyPushError();
  docs.value = res.result;
});

function deleteUser(userId: string) {
  Modal.confirm({
    title: '確定刪除此管理員？',
    icon: createVNode(ExclamationCircleOutlined),
    async onOk() {
      const res = await developmentApi.Administrator.set(
          userId, false, '');
      if (res.status !== 0) throw notifyPushError();
      docs.value = res.result;
    },
    onCancel() {
      console.log('Cancel');
    },
    class: 'test',
  });
}

function addUser() {
  Modal.confirm({
    title: '確定新增此管理員？',
    icon: createVNode(ExclamationCircleOutlined),
    async onOk() {
      const res = await developmentApi.Administrator.set(
          userIdInput.value, true, descriptionInput.value);
      if (res.status === 2) throw notifyPushError("資料有誤");
      if (res.status !== 0) throw notifyPushError();
      userIdInput.value = '';
      descriptionInput.value = '';
      docs.value = res.result;
    },
    class: 'test',
  });
}
</script>
<style scoped>

</style>
