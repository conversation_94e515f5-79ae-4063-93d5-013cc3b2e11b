<template>
  <a-upload :before-upload="beforeUpload" :file-list="fileList" :showUploadList="props.showUploadList"
            accept=".bmp,.jpg,.jpeg,.png,.gif"
            method="get"
            @remove="handleRemove">
    <a-button>
      <upload-outlined></upload-outlined>
      Select File
    </a-button>
  </a-upload>
</template>
<script lang="ts" setup>
import {UploadOutlined} from '@ant-design/icons-vue';
import type {UploadProps} from 'ant-design-vue';
import {notifyPushError} from "@/services/notify.ts";
import {uploadImageToS3ByBase64} from "@/services/aws.ts";
import {FileListType} from "@/components/Order/Audit/UploadTool/UploadToolType.ts";
import {v4 as uuidv4} from 'uuid';

const props = withDefaults(defineProps<{
  s3Path: string;
  showUploadList: boolean;
}>(), {
  s3Path: 'other',
  showUploadList: () => true,
});

const emits = defineEmits<{
  (e: 'change', item: FileListType[]): void;
}>();

const fileList = defineModel<FileListType[]>('files', {
  type: Array<FileListType>,
  default: () => [],
});

const handleRemove: UploadProps['onRemove'] = _file => {
  const file = _file as any as FileListType;
  const index = fileList.value.findIndex(item => item.uid === file.uid);
  if (index === -1) throw notifyPushError();
  fileList.value.splice(index, 1);
  emits('change', fileList.value);
};

const beforeUpload: UploadProps['beforeUpload'] = async file => {
  // console.log('beforeUpload:', file);
  const uid = uuidv4();
  fileList.value.push({
    uid,
    name: file.name,
    file,
    url: '',
  });
  // 上傳 S3 取得 URL
  const reader = new FileReader();
  reader.readAsDataURL(file as any);
  const url = await new Promise<string>((resolve) => {
    reader.onloadend = async e => {
      const base64 = e.target?.result;
      if (!base64 || typeof base64 !== "string") throw notifyPushError();
      const key = `${props.s3Path}/${uid}.${file.type.split('/')[1]}`;
      const url = await uploadImageToS3ByBase64(base64, key, file.type);
      resolve(url);
    };
  });
  console.log(url);
  // 將 URL 加回 fileList
  const target = fileList.value.find(item => item.uid === uid);
  if (target) target.url = url;
  emits('change', fileList.value);
};
</script>
