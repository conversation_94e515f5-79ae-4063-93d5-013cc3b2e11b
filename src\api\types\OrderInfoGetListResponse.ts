import { ResponseBase } from "@/api/types/ResponseBase.ts";
import { OrderType } from "@/types/OrderType.ts";
import { Address } from "@/api/types/CustomerInvoiceGetResponse.ts";
import { AuthType } from "@/types/AuthType.ts";
import {
  ConstructionStatus,
  DesignStatus,
  MeasureStatus,
} from "@/types/OrderStatus.ts";

export interface OrderInfoGetListResponse extends ResponseBase {
  result: OrderInfoResult;
}

export interface OrderInfoResult {
  total: number;
  orders: OrderInfoItem[];
}

export interface OrderInfoItem {
  index: number;
  createTime: string;
  refreshTime: string;
  orderType: OrderType;
  orderId: string;
  isDeleted: boolean;
  customerId: string;
  customerName: string;
  address: Address;
  designerId: string;
  designerName: string;
  measureItem: MeasureItem;
  designItem: DesignItem;
  constructionItem: ConstructionItem;
}

export interface MeasureItem {
  status: MeasureStatus;
  userType: AuthType;
}

export interface DesignItem {
  status: DesignStatus;
}

export interface ConstructionItem {
  status: ConstructionStatus;
}
