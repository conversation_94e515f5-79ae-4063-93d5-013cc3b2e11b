import {postApi, postApiByToken} from "@/api/baseApi.ts";
import {GetGuestTokenResponse} from "@/api/types/GetGuestTokenResponse.ts";
import {LoginResponse} from "@/api/types/LoginResponse.ts";
import packageJson from "@/../package.json";

export default {
  GetGuestToken: async (navigator: Navigator) =>
    await postApi<GetGuestTokenResponse>(
      'Security/Manager/GetGuestToken',
      {
        deviceType: 3,
        deviceBrand: getBrowserName(navigator),
        deviceModel: /Mobi|Android|iPhone/i.test(navigator.userAgent) ? "Mobile" : "Desktop",
        deviceVersion: navigator.userAgent,
        deviceRegion: navigator.language,
        appVersion: packageJson.version,
        fcmToken: ""
      }),

  login: async (username: string, password: string, token: string) =>
    await postApi<LoginResponse>(
      'Security/Manager/Login',
      {username, password}, token),

  mountEvent: async (navigator: Navigator, fcmToken: string) =>
    await postApiByToken<LoginResponse>(
      'Security/MountEvent',
      {
        deviceVersion: navigator.userAgent,
        appVersion: packageJson.version,
        fcmToken
      }),
}

/**
 * 取得目前(2024)常用的瀏覽器名稱，不包含全部
 * @param navigator window.navigator
 */
function getBrowserName(navigator: Navigator): string {
  const browserName = navigator.userAgent.toLowerCase();
  if (/msie/i.test(browserName) && !/opera/.test(browserName)) return "IE";
  if (/firefox/i.test(browserName)) return "Firefox";
  if (/chrome/i.test(browserName) && /webkit/i.test(browserName) && /mozilla/i.test(browserName)) return "Chrome";
  if (/opera/i.test(browserName)) return "Opera";
  if (/webkit/i.test(browserName) && !(/chrome/i.test(browserName) && /webkit/i.test(browserName) && /mozilla/i.test(browserName))) return "Safari";
  return "";
}
