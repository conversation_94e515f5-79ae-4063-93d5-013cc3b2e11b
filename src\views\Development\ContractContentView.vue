<template>
  <div class="contract-flex-row">
    <div style="display: flex; align-items: center;">
      <span>訂單金額：</span>
      <a-input-number v-model:value="orderAmount" :min="0" style="width: 100px;" />
    </div>
    <div style="display: flex; align-items: center;">
      <span>折扣後金額：</span>
      <a-input-number v-model:value="discountAmount" :min="0" style="width: 100px;" />
    </div>
  </div>
  <a-tabs v-model:activeKey="activeKey" style="height: 100%;">
    <a-tab-pane v-for="type in contractTypes" :key="type" :tab="ContractTypeDescriptions[type]">
      <div style="display: flex; flex-direction: row; gap: 32px; align-items: flex-start;">
        <!-- 左：編輯器 + 操作 -->
        <div class="contract">
          <div style="font-weight: bold; font-size: 18px;">編輯內容</div>
          <a-textarea v-model:value="draftContent[type]" :auto-size="{ minRows: 16 }" placeholder="請輸入 HTML 內容"
            style="resize: vertical;" />
          <div style="margin-top: 8px;">
            <span style="font-weight: 500;">當前版本：</span>
            <span>{{ contractMap[type]?.version ?? '-' }}</span>
          </div>
          <a-button type="primary" @click="update(type)"
            style="width: 100%; margin-top: 8px;">更新</a-button>
        </div>

        <!-- 右：預覽區 -->
        <div class="contract">
          <div style="font-weight: bold; font-size: 18px;">預覽內容</div>
          <div class="preview" style="flex:1; min-height: 300px; color: #222;">
            <div v-html="previewContent(type)"></div>
          </div>
        </div>
      </div>
    </a-tab-pane>
  </a-tabs>
</template>

<script lang="ts" setup>
import { ContractContentGetItem } from "@/api/types/ContractContentGetResponse.ts";
import { ref, reactive, createVNode } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { notifyPushError } from "@/services/notify.ts";
import developmentApi from "@/api/developmentApi.ts";
import { ContractType, ContractTypeDescriptions } from "@/types/ContractType.ts";

const orderAmount = ref<number>(0);
const discountAmount = ref<number>(0);
// 1. 標籤型態 & 描述
const contractTypes = [
  ContractType.Step2Designer,
  ContractType.Step2Customer,
  ContractType.Step3NormalDesigner,
  ContractType.Step3NormalCustomer,
  ContractType.Step3ContinueDesigner,
  ContractType.Step3ContinueCustomer,
];

// 2. 存放查詢結果
const contractMap = ref<Partial<Record<ContractType, ContractContentGetItem>>>({});
const draftContent = reactive<Partial<Record<ContractType, string>>>({});

// 3. 狀態控制
const activeKey = ref<ContractType>(ContractType.Step2Designer);

function previewContent(type: ContractType) {
  let content = draftContent[type] || '';
  // 只做顯示替換，送出時 draftContent 內容不變
  content = content
    .replace(/{{\s*amount\s*}}/g, String(orderAmount.value))
    .replace(/{{\s*discountAmount\s*}}/g, String(discountAmount.value));
  return content;
}

//取得合約
const fetchData = async () => {
  try {
    const res = await developmentApi.contractContent.get();
    if (res.status !== 0) throw notifyPushError();
    contractMap.value = {};
    res.result.forEach((item) => {
      contractMap.value[item.type] = item;
      draftContent[item.type] = item.contentHtml;
    });
  } catch (e) {
    message.error("取得資料失敗");
  }
};

fetchData(); // 頁面初始化時呼叫

//更新合約
const update = async (type: ContractType) => {
  Modal.confirm({
    title: '確定更新合約版本',
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode(
      'div',
      { style: 'color:red;' },
      ['！注意！非開發人員請勿操作此設置！亂修改可能產生法律問題！注意！',
        createVNode('div', { style: 'margin-top:12px;color:black;' },
          ['將「',ContractTypeDescriptions[type],'」設為 version = ',
            (contractMap.value[type]?.version ?? 0) + 1,' 版本。']),]),
    async onOk() {
      try {
        const html = draftContent[type] || "";
        const Version = contractMap.value[type]?.version ?? 0;
        const res = await developmentApi.contractContent.set(
          html,
          Version,
          type,
        );
        if (res.status !== 0) throw notifyPushError();
        res.result.forEach((item) => {
          contractMap.value[item.type] = item;
          draftContent[item.type] = item.contentHtml;
        });
        message.success("更新成功");
      } catch (e) {
        message.error("更新失敗");
      }
    },
    onCancel() {
      // 無需特別處理
    },
  });
};
</script>
<style>
.contract-flex-row {
  display: flex;
  flex-direction: row;
  gap: 32px;
  align-items: flex-start;
}
.contract {
  width: 500px;
  background: #f0f0f0;
  padding: 32px 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.preview h1 {
  font-size: 1.5rem !important;
  font-weight: bold !important;
  margin: 0.67em 0 !important;
}
.preview h2 {
  font-size: 1.25rem !important;
  font-weight: bold !important;
  margin: 0.75em 0 !important;
}
@media (max-width: 500px) {
  .contract-flex-row {
    flex-direction: column;
    gap: 24px;
    align-items: stretch;
  }
}
</style>
