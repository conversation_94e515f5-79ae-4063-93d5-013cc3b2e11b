import {RouterMapType} from "@/router/types.ts";
import {
  BugOutlined,
  DatabaseOutlined,
  DesktopOutlined,
  FileTextOutlined,
  SettingOutlined,
  UserOutlined,
  VideoCameraOutlined,
} from '@ant-design/icons-vue';

export const routerMap: RouterMapType[] = [
  {
    key: "dashboard",
    name: "儀錶板",
    path: "/",
    icon: DesktopOutlined,
    type: "top",
    component: () => import('@/views/Dashboard/DashboardView.vue'),
  },
  {
    key: "order",
    name: "訂單相關",
    icon: FileTextOutlined,
    type: "body",
    body: [
      {
        key: "orderInfo",
        name: "訂單列表",
        path: "/order/Info",
        component: () => import('@/views/Order/OrderInfoView.vue'),
      },
      {
        key: "orderAudit",
        name: "審核列表",
        path: "/order/audit",
        component: () => import('@/views/Order/OrderAuditView.vue'),
      },
    ],
  },
  {
    key: "user",
    name: "用戶相關",
    icon: UserOutlined,
    type: "body",
    body: [
      {
        key: "userProfile",
        name: "用戶列表",
        path: "/user/profile",
        component: () => import('@/views/User/UserProfileView.vue'),
      },
      {
        key: "userAudit",
        name: "審核列表",
        path: "/user/audit",
        component: () => import('@/views/User/UserAuditView.vue'),
      },
    ],
  },
  {
    key: "video",
    name: "影片管理",
    path: "/video",
    icon: VideoCameraOutlined,
    type: "top",
    component: () => import('@/views/Video/VideoView.vue'),
  },
  {
    key: "appData",
    name: "APP 資料",
    icon: DatabaseOutlined,
    type: "body",
    body: [
      {
        key: "platformRemittanceAccount",
        name: "平台匯款帳號",
        path: "/appData/platformRemittanceAccount",
        component: () => import('@/views/AppData/PlatformRemittanceAccountView.vue'),
      },
      {
        key: "administrator",
        name: "超級管理員帳號",
        path: "/appData/administrator",
        component: () => import('@/views/AppData/AdministratorView.vue'),
      },
    ],
  },
  {
    key: "development",
    name: "開發者頁面",
    icon: BugOutlined,
    type: "body",
    body: [
      {
        key: "devAppVersion",
        name: "APP 版本管理",
        path: "/dev/app-version",
        component: () => import('@/views/Development/AppVersionView.vue'),
      },
      {
        key: "contractContent",
        name: "合約內容修改",
        path: "/dev/contract-content",
        component: () => import('@/views/Development/ContractContentView.vue'),
      },
      {
        key: "signalrTest",
        name: "SignalR 測試",
        path: "/dev/signalr-test",
        component: () => import('@/views/Development/SignalRTestView.vue'),
      },
      {
        key: "systemEnv",
        name: "系統環境變數",
        path: "/dev/system-env",
        component: () => import('@/views/Development/AboutView.vue'),
      },
      {
        key: "devTestPage",
        name: "開發測試頁面",
        path: "/dev/test-page",
        component: () => import('@/views/Development/TestPageView.vue'),
      },
    ],
  },
  {
    key: "systemSetting",
    name: "系統設定",
    path: "/system-setting",
    icon: SettingOutlined,
    type: "bottom",
    component: () => import('@/views/SystemSetting/SystemSettingView.vue'),
  },
];
