import {postApi} from "@/api/baseApi";
import {ChatRoomAsyncResponse} from "@/api/types/ChatRoomAsyncResponse.ts";
import {ChatMessageAsyncResponse} from "@/api/types/ChatMessageAsyncResponse.ts";
import {ResponseBase} from "@/api/types/ResponseBase.ts";
import {ChatMessageSendResponse} from "@/api/types/ChatMessageSendResponse.ts";

let _token: string = '';

export default {
  setToken: (token: string) => {
    _token = token;
  },

  roomAsync: async (): Promise<ChatRoomAsyncResponse | null> =>
    await postApi('Chat/Room/Async', {}, _token),

  messageAsync: async (lastMessageId: string): Promise<ChatMessageAsyncResponse | null> =>
    await postApi('Chat/Message/Async', {
      lastMessageId,
    }, _token),

  messageSend: async (roomId: string, text: string): Promise<ChatMessageSendResponse | null> =>
    await postApi('Chat/Message/Send', {
      roomId,
      message: {
        type: 2,
        text,
      }
    }, _token),

  messageUnsend: async (roomId: string, messageId: string): Promise<ResponseBase | null> =>
    await postApi('Chat/Message/Unsend', {
      roomId, messageId,
    }, _token),
}
