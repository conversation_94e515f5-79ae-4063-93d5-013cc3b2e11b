<template>
  <a-layout style="height: 100vh">
    <a-layout-sider v-model:collapsed="collapsed" :collapsed-width="isSmallScreen ? 0 : undefined"
                    :trigger="null" collapsible
                    style="overflow: auto">
      <RouterLink style="display: flex; align-items: center; margin-top: 16px" to="/">
        <HomeOutlined style="font-size: 32px; color: white; margin-left: 24px; padding-bottom: 12px"/>
        <h1 v-if="!collapsed" style="font-size: 24px; color: white; margin-left: 8px">HomeEasy</h1>
      </RouterLink>
      <a-menu v-model:selectedKeys="selectedKeys" mode="inline" theme="dark">
        <template v-for="menuItem in routerMapRef as RouterMapType[]">
          <a-sub-menu v-if="menuItem.type === 'body'" :key="menuItem.key">
            <template #title>
            <span>
              <component :is="menuItem.icon"/>
              <span>{{ menuItem.name }}</span>
            </span>
            </template>
            <a-menu-item v-for="subMenu in (menuItem as RouterRecordWithBody).body" :key="subMenu.key">
              {{ subMenu.name }}
            </a-menu-item>
          </a-sub-menu>
          <a-menu-item v-if="menuItem.type !== 'body'" :key="menuItem.key">
            <component :is="menuItem.icon"/>
            <span>{{ menuItem.name }}</span>
          </a-menu-item>
        </template>
      </a-menu>
    </a-layout-sider>
    <a-layout>
      <a-layout-header style="background: #fff; padding: 0">
        <div style="display: flex; overflow: hidden; margin: 0 8px">
          <div>
            <menu-unfold-outlined v-if="collapsed" class="header-button-icon" @click="() => (collapsed = !collapsed)"/>
            <menu-fold-outlined v-else class="header-button-icon" @click="() => (collapsed = !collapsed)"/>
          </div>
          <div style="flex-grow: 1; display: flex; overflow: hidden">
            <div>
              <RedoOutlined v-if="!loading" class="header-button-icon" @click="refreshPage()"/>
              <LoadingOutlined v-else class="header-button-icon"/>
            </div>
          </div>
          <div>
            <LogoutOutlined class="header-button-icon" @click="useLoginStatus.setLogOut()"/>
          </div>
        </div>
      </a-layout-header>
      <a-layout-content style="overflow: auto; padding: 0 16px">
        <div style="margin: 16px 0">
          <a-breadcrumb style="margin: 16px 0">
            <a-breadcrumb-item v-for="name in navigationBar">{{ name }}</a-breadcrumb-item>
          </a-breadcrumb>
          <div style="padding: 16px; background: #fff">
            <slot></slot>
          </div>
          <p style="padding: 16px; text-align: center; color: gray">
            HomeEasy ©2024 Created by MMSLAB
          </p>
        </div>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>
<script lang="ts" setup>
import {
  HomeOutlined,
  LoadingOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  RedoOutlined,
} from '@ant-design/icons-vue';
import {ref, watch} from 'vue';
import {routerMap} from "@/router/config.ts";
import {useRouter} from "vue-router";
import {RouterMapType, RouterRecordWithBody} from "@/router/types.ts";
import {userStatusStore} from "@/stores/userStatusStore.ts";
import {useMediaQuery} from '@vueuse/core'

const isSmallScreen = useMediaQuery('(max-width: 576px)')
const useLoginStatus = userStatusStore();
const emit = defineEmits(['reload']);
const router = useRouter();

const routerMapRef = ref(routerMap);
const collapsed = ref<boolean>(true);
const selectedKeys = ref<string[]>([routerMap[0].key]);
const navigationBar = ref<string[]>(['首頁', routerMap[0].name]);
const loading = ref<boolean>(false);

// 監聽path變化改變selectedKeys
watch(() => router.currentRoute.value.path, () => {
  const routerName = router.currentRoute.value.name;
  selectedKeys.value = [routerName as string];
}, {immediate: true});

// 監聽selectedKeys變化改變顯示網頁
watch(selectedKeys, (newKeys) => {
  refreshPage(newKeys[0]);
});

const refreshPage = async (key?: string) => {
  loading.value = true;
  const routerMapItem = routerMap
      .flatMap(e => e.type === 'body'
          ? e.body.map(f => ({key: f.key, names: [e.name, f.name], path: f.path}))
          : {key: e.key, names: [e.name], path: e.path})
      .map(e => ({key: e.key, names: e.names, path: e.path}))
      .find(e => key ? e.key === key : e.path === window.location.pathname);
  if (!routerMapItem) return;
  selectedKeys.value[0] = routerMapItem.key;
  navigationBar.value = ['首頁', ...routerMapItem.names];
  if (window.location.pathname !== routerMapItem.path) {
    console.log(`[router] push -> ${routerMapItem.path}`);
    await router.push(routerMapItem.path);
  } else {
    console.log(`[router] reload -> ${routerMapItem.path}`);
    emit('reload');
  }
  await new Promise(resolve => setTimeout(resolve, 1000));
  loading.value = false;
};
</script>
<style scoped>
.header-button-icon {
  font-size: 20px;
  margin: 8px 8px;
}
</style>
