export function pushNotification(body: string) {
  if (Notification.permission === 'granted') {
    new Notification("HomeEasy CMS", {
      body, icon: "../src/assets/homeeasy_icon.svg"
    });
  }
}

export async function requestNotificationPermission() {
  if (Notification.permission !== "default") return Notification.permission;
  const permission = await Notification.requestPermission();
  if (permission === 'granted') pushNotification("已開啟通知功能");
  return permission;
}
