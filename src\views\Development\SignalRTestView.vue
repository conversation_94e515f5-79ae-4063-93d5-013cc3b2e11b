<template>
  <h2>SignalR 測試 (<a>{{ signalRHub }}</a>)</h2>
  <a-descriptions :column="1" bordered size="small" style="margin-bottom: 16px">
    <a-descriptions-item label="連線狀態">
      <a-button v-if="isConnected" style="width: 100px" type="primary" @click="disconnect()">
        已連線
      </a-button>
      <a-button v-else danger style="width: 100px" @click="connection()">
        未連線
      </a-button>
    </a-descriptions-item>
    <a-descriptions-item label="註冊狀態">
      <a-tag v-if="!isConnected">未連線</a-tag>
      <a-tag v-else-if="registerIsSuccess" color="success">成功</a-tag>
      <a-tag v-else color="error">失敗</a-tag>
    </a-descriptions-item>
    <a-descriptions-item label="Token">
      <a-input v-model:value="token" :disabled="isConnected" style="width: 300px"/>
    </a-descriptions-item>
    <a-descriptions-item label="ConnectId">{{ connectId }}</a-descriptions-item>
  </a-descriptions>
  <a-flex>
    <div style="width: 400px; margin-right: 16px">
      <a-descriptions :column="1" bordered size="small" style="margin-bottom: 16px">
        <a-descriptions-item label="取得聊天室列表">
          <a-button :disabled="!isConnected" type="primary" @click="roomAsync()">
            取得
          </a-button>
          {{ Object.keys(rooms).length }} / {{ roomApiProgress }}
        </a-descriptions-item>
        <a-descriptions-item label="取得歷史訊息">
          <a-button :disabled="!isConnected" type="primary" @click="messageAsync(lastMessageId)">
            取得
          </a-button>
          {{ Object.keys(messages).length }} / {{ messagesApiProgress }}
        </a-descriptions-item>
        <a-descriptions-item label="最後取得訊息ID">
          {{ lastMessageId }}
        </a-descriptions-item>
        <a-descriptions-item label="發送測試訊息">
          <a-input v-model:value="sendMessageText" style="width: 150px; margin-right: 8px"/>
          <a-button :disabled="!isConnected" type="primary" @click="messageSend(chatRoomTab, sendMessageText)">
            送出
          </a-button>
        </a-descriptions-item>
        <a-descriptions-item label="發送測試">
          <a-button :disabled="!isConnected" type="primary" @click="test()">
            發送
          </a-button>
        </a-descriptions-item>
      </a-descriptions>
    </div>
    <div>
      <a-tabs v-model:activeKey="chatRoomTab">
        <a-tab-pane v-for="room in rooms" :key="room.roomId" :tab="room.roomName">
          <a-image :src="room.roomIcon" height="64px"/>
          <a-card size="small" style="background-color: #b4b4b4">
            <div style="overflow: auto; max-height: 300px; margin: 8px;">
              <div v-for="message in showMessages" class="message" style="width: fit-content">
                <a-button size="small" type="primary" @click="messagesUnsend(message.roomId, message.messageId)">
                  收回
                </a-button>
                {{ message.messageId }}：{{
                  message.status === ChatMessageStatus.Unsend ? '(收回)' :
                      message.message.type === MessageContentType.Text ? message.message.text : '(其他格式)'
                }}
              </div>
            </div>
          </a-card>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-flex>
  <a-card>
    <h2>Console Log</h2>
    <div style="overflow: auto; height: 300px">
      <a-card v-for="log in consoleLog" size="small">
        <h3>[{{ log.time.getTime() }}] {{ log.method }}</h3>
        <div style="background-color: gainsboro; padding: 8px 8px; border-radius: 10px">
          {{ log.message }}
        </div>
      </a-card>
    </div>
  </a-card>
</template>
<script lang="ts" setup>
import {computed, onBeforeUnmount, ref} from 'vue';
import {ChatHubClass} from "@/services/signalR.ts";
import chatApi from "@/api/chatApi.ts";
import {ChatMessageStatus, messageContent, MessageContentType} from "@/api/types/ChatMessageSendResponse.ts";

const signalRHub = import.meta.env.VITE_SIGNALR_HUB;
const chatHub = new ChatHubClass(signalRHub);

const token = ref<string>("55140f46fa58454697b35305ea68c921");
const isConnected = ref<boolean>(false);
const connectId = ref<string>("");
const registerIsSuccess = ref<boolean>(false);
const consoleLog = ref<ConsoleLogType[]>([]);
const sendMessageText = ref<string>("Hello World!");
const messages = ref<MessageDataRef>({});
const chatRoomTab = ref<string>("");
const rooms = ref<RoomDataRef>({});
const roomApiProgress = ref<number>(0);
const messagesApiProgress = ref<number>(0);
const lastMessageId = ref<string>("");

const showMessages = computed(() => {
  return Object.values(messages.value)
      .filter(e => e.roomId === chatRoomTab.value)
      .sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime()) as MessageData[];
});

const roomAsync = async () => {
  const result = await chatApi.roomAsync();
  if (!result) throw new Error('roomAsync error');
  roomApiProgress.value = result.roomCount;
};

const messageAsync = async (lastMessageId: string = '') => {
  const result = await chatApi.messageAsync(lastMessageId);
  if (!result) throw new Error('messageAsync error');
  messagesApiProgress.value = result.messageCount;
};

const messageSend = async (roomId: string, text: string) => {
  const response = await chatApi.messageSend(roomId, text);
  if (!response) throw new Error('messageSend error');
  messages.value[response.message.messageId] = response.message;
};

const messagesUnsend = async (roomId: string, messageId: string) => {
  await chatApi.messageUnsend(roomId, messageId);
};

const test = async () => {
  await chatHub.invoke("test", "Hello World!");
};

const connection = async () => {
  chatApi.setToken(token.value);
  await chatHub.connection();
  chatHub.onclose(() => {
    isConnected.value = false;
  });
  chatHub.on("connect", (isSuccess: boolean) => {
    console.log(`connect -> ${isSuccess}`);
    connectId.value = chatHub.connectionId;
    registerIsSuccess.value = isSuccess;
    consoleLog.value.unshift({
      time: new Date(),
      method: "connect",
      message: JSON.stringify(isSuccess)
    });
  });
  chatHub.on("message", (data: MessageData, asyncType: MessageAsyncType, progressId: string) => {
    messages.value[data.messageId] = data;
    lastMessageId.value = data.messageId;
    console.log(`${progressId} message (${asyncType}) -> ${JSON.stringify(data)}`);
    consoleLog.value.unshift({
      time: new Date(),
      method: `message ${progressId}`,
      message: JSON.stringify(data)
    });
  });
  chatHub.on("message/unsend", (messageId: string, progressId: string) => {
    if (messages.value[messageId]) {
      messages.value[messageId].refreshTime = new Date().toISOString();
      messages.value[messageId].status = ChatMessageStatus.Unsend;
      messages.value[messageId].message = {
        type: MessageContentType.None,
      };
    }
    console.log(`${progressId} message/unsend -> ${messageId}`);
    consoleLog.value.unshift({
      time: new Date(),
      method: "message/unsend",
      message: messageId,
    });
  });
  chatHub.on("test", (msg: any) => {
    console.log(`test -> ${JSON.stringify(msg)}`);
    consoleLog.value.unshift({
      time: new Date(),
      method: "test",
      message: JSON.stringify(msg)
    });
  });
  chatHub.on("room", (msg: RoomData) => {
    rooms.value[msg.roomId] = msg;
    if (chatRoomTab.value === "") chatRoomTab.value = msg.roomId;
    console.log(`room -> ${JSON.stringify(msg)}`);
    consoleLog.value.unshift({
      time: new Date(),
      method: "room",
      message: JSON.stringify(msg)
    });
  });
  chatHub.on("message/read", (userId: string, roomId: string, messageId: string) => {
    console.log(`message/read -> ${userId}, ${roomId}, ${messageId}`);
    consoleLog.value.unshift({
      time: new Date(),
      method: "message/read",
      message: `${userId}, ${roomId}, ${messageId}`
    });
  });
  await chatHub.invoke("connect", token.value);
  isConnected.value = true;
};

const disconnect = async () => {
  await chatHub.disconnect();
  connectId.value = "";
  isConnected.value = false;
  registerIsSuccess.value = false;
};

onBeforeUnmount(() => {
  disconnect()
});

interface ConsoleLogType {
  time: Date;
  method: string;
  message: string;
}

interface MessageDataRef {
  [key: string]: MessageData;
}

interface MessageData {
  messageId: string;
  createTime: string;
  refreshTime: string;
  status: ChatMessageStatus;
  roomId: string;
  userId: string;
  message: messageContent;
}

interface RoomDataRef {
  [key: string]: RoomData;
}

interface RoomData {
  roomId: string;
  createTime: string;
  refreshTime: string;
  Type: RoomType;
  ownerId: string;
  userIds: string[];
  roomName: string;
  roomIcon: string;
  notify: boolean;
  userConfigs: {
    [key: string]: ChatUserItem
  };
}

interface ChatUserItem {
  userType: UserType;
  userId: string;
  username: string;
  avatarUrl: string;
  readLastMessageId: string;
}


enum RoomType {
  Private = 1,
  Group = 2,
  Order = 3,
}

enum UserType {
  None = -1,
  Customer = 0,
  Designer = 1,
  Manager = 2,
}

enum MessageAsyncType {
  Normal = 0,
  MessageAsync = 1,
  MessageOneRoomAsync = 2,
}
</script>
<style scoped>
.message {
  margin-bottom: 8px;
  border-radius: 4px;
  padding: 4px;
  background-color: antiquewhite;
}
</style>