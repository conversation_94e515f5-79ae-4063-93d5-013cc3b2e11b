<template>
  <p v-if="value.verifyStatus === VerifyStatus.Init">未上傳</p>
  <a v-else-if="value.verifyStatus === VerifyStatus.Verifying" @click="showModal">前往驗證>></a>
  <a v-else-if="value.verifyStatus === VerifyStatus.Verified" style="color: green;" @click="showModal">已驗證</a>
  <a v-else-if="value.verifyStatus === VerifyStatus.VerifyFail" style="color: red;"
     @click="showModal">驗證失敗，等待重新上傳</a>

  <a-modal v-model:open="open" :title="title" @ok="handleSuccess">
    <a-form :label-col="{style: {width: '100px'}}" :wrapper-col="{span: 16}">
      <a-form-item label="上傳時間">
        <a-input :value="new Date(value.updateTime).toLocaleString()" disabled/>
      </a-form-item>
      <a-form-item label="用戶名稱">
        <a-input :value="value.username" disabled/>
      </a-form-item>
      <a-form-item label="身分證字號">
        <a-input v-model:value="value.idNumber" :disabled="value.verifyStatus !== VerifyStatus.Verifying"/>
        <a-flex style="margin-top: 8px;">
          <a-image :src="value.frontSideUrl" width="100%"/>
          <div style="width: 8px;"/>
          <a-image :src="value.backSideUrl" width="100%"/>
        </a-flex>
      </a-form-item>
      <a-form-item label="性別">
        <a-radio-group v-model:value="value.gender" :disabled="value.verifyStatus !== VerifyStatus.Verifying">
          <a-radio :value="Gender.Unknown">未知</a-radio>
          <a-radio :value="Gender.Male">男</a-radio>
          <a-radio :value="Gender.Female">女</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button key="submit" :disabled="buttonDisabled" :loading="failButtonLoading" danger @click="handleFail">
        審核失敗
      </a-button>
      <a-button key="submit" :disabled="buttonDisabled" :loading="successButtonLoading" type="primary"
                @click="handleSuccess">審核通過
      </a-button>
    </template>
  </a-modal>
</template>
<script lang="ts" setup>
import {computed, ref} from 'vue';
import {VerifyStatus} from "@/types/VerifyStatus.ts";
import {Gender} from "@/types/Gender.ts";
import {notifyPush, notifyPushError, notifyType} from "@/services/notify.ts";
import userApi from "@/api/userApi.ts";
import {UserAuditTaiwanId} from "@/api/types/UserAuditGetOneResponse.ts";

const title = ref<string>('Title');
const successButtonLoading = ref<boolean>(false);
const failButtonLoading = ref<boolean>(false);
const buttonDisabled = ref<boolean>(false);
const open = ref<boolean>(false);

const props = defineProps(['userId', 'modelValue', 'userType']);
const emit = defineEmits(['update:modelValue']);
const value = computed({
  get() {
    return props.modelValue as UserAuditTaiwanId;
  },
  set(value) {
    emit('update:modelValue', value);
  }
});

const showModal = () => {
  console.log(value.value);
  switch (value.value.verifyStatus) {
    default:
      notifyPushError();
      return;
    case VerifyStatus.Verifying:
      buttonDisabled.value = false;
      title.value = '進行審核中';
      break;
    case VerifyStatus.Verified:
      buttonDisabled.value = true;
      title.value = '已完成審核';
      break;
    case VerifyStatus.VerifyFail:
      buttonDisabled.value = true;
      title.value = '審核失敗';
      break;
  }
  open.value = true;
};

const handleSuccess = async () => {
  if (value.value.idNumber === '' || value.value.gender === Gender.Unknown) {
    notifyPush({
      type: notifyType.warning,
      message: '請填寫完整',
      description: '身分證字號和性別不可為空'
    });
    return;
  }
  title.value = '已完成審核';
  buttonDisabled.value = true;
  successButtonLoading.value = true;
  const response = await userApi.audit.taiwanIdVerify(
      props.userId, true, props.userType, value.value.gender, value.value.idNumber);
  if (!response || response.status !== 0) notifyPushError();
  else value.value.verifyStatus = VerifyStatus.Verified;
  successButtonLoading.value = false;
  open.value = false;
};

const handleFail = async () => {
  title.value = '審核失敗';
  buttonDisabled.value = true;
  failButtonLoading.value = true;
  const response = await userApi.audit.taiwanIdVerify(
      props.userId, false, props.userType, value.value.gender, value.value.idNumber);
  if (!response || response.status !== 0) notifyPushError();
  else value.value.verifyStatus = VerifyStatus.VerifyFail;
  failButtonLoading.value = false;
  open.value = false;
};
</script>

