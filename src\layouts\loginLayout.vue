<template>
  <a-flex align="center" justify="center" style="height: 100vh;">
    <a-spin :spinning="spinning" tip="Loading...">
      <a-card style="max-width: 500px; width: 50vw; margin: 0 8px">
        <a-form
            :model="formState"
            class="login-form"
            name="normal_login"
            @finish="onFinish"
        >
          <a-form-item
              :rules="[{ required: true, message: 'Please input your username!' }]"
              label="Username"
              name="username"
          >
            <a-input v-model:value="formState.username">
              <template #prefix>
                <UserOutlined class="site-form-item-icon"/>
              </template>
            </a-input>
          </a-form-item>

          <a-form-item
              :rules="[{ required: true, message: 'Please input your password!' }]"
              label="Password"
              name="password"
          >
            <a-input-password v-model:value="formState.password">
              <template #prefix>
                <LockOutlined class="site-form-item-icon"/>
              </template>
            </a-input-password>
          </a-form-item>
          <a-flex justify="space-between" wrap>
            <a-form-item>
              <a-form-item name="remember" no-style>
                <a-checkbox v-model:checked="formState.remember">Remember me</a-checkbox>
              </a-form-item>
            </a-form-item>
            <a-form-item>
              <a-button class="login-form-button" html-type="submit" type="primary">
                Log in
              </a-button>
            </a-form-item>
          </a-flex>
        </a-form>
      </a-card>
    </a-spin>
  </a-flex>
</template>
<script lang="ts" setup>
import securityApi from "@/api/securityApi.ts";
import {reactive, ref} from "vue";
import {LockOutlined, UserOutlined} from '@ant-design/icons-vue';
import {sha256} from "@/services/crypto.ts";
import {notifyPush, notifyPushError, notifyType} from "@/services/notify.ts";
import {userStatusStore} from "@/stores/userStatusStore.ts";

const userStatus = userStatusStore();
const spinning = ref<boolean>(false);
const formState = reactive({
  username: '',
  password: '',
  remember: false,
});

const onFinish = async (values: FormState) => {
  spinning.value = true;
  const GetGuestTokenResult = await securityApi.GetGuestToken(navigator);
  if (GetGuestTokenResult.status !== 0) {
    notifyPushError();
    return;
  }
  const token = GetGuestTokenResult.token;
  const loginResult = await securityApi.login(values.username, sha256(values.password), token);
  if (loginResult.status === -1) notifyPushError();
  else if (loginResult.status === 800) {
    notifyPush({
      type: notifyType.error,
      message: '登入失敗',
      description: '帳號或密碼錯誤',
    });
  } else {
    await userStatus.setLogIn(loginResult.userId, token, values.remember);
    notifyPush({
      type: notifyType.success,
      message: '登入成功',
      description: '歡迎回來',
    });
  }
  spinning.value = false;
};

interface FormState {
  username: string;
  password: string;
  remember: boolean;
}
</script>
<style scoped>
</style>
