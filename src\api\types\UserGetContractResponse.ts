import {ResponseBase} from "@/api/types/ResponseBase.ts";
import {OrderType} from "@/types/OrderType.ts";
import {UserType} from "@/types/UserType.ts";

export interface UserGetContractResponse extends ResponseBase {
  result: UserGetContractItem[];
}

export interface UserGetContractItem {
  orderType: OrderType;
  orderId: string;
  updateTime: string;
  contentHtml: string;
  signatureImageUrl: string;
  userType: UserType;
  userId: string;
  userTaiwanIdName: string;
  orderCustomerName: string;
}
