<template>
  <login-layout v-if="!useLoginStatus.isLoggedIn"/>
  <sider-layout v-else @reload="reload()">
    <router-view v-if="isRouterAlive"/>
  </sider-layout>
</template>
<script lang="ts" setup>
import SiderLayout from "@/layouts/siderLayout.vue";
import {nextTick, ref} from "vue";
import LoginLayout from "@/layouts/loginLayout.vue";
import {userStatusStore} from "@/stores/userStatusStore.ts";
import {useRouter} from "vue-router";

const useLoginStatus = userStatusStore();
const isRouterAlive = ref<boolean>(true);

useRouter().beforeEach((to) => {
  if (to.name != "dashboard" && to.meta.title)
    document.title = `${to.meta.title} - HomeEasy MS`;
  else
    document.title = "HomeEasy MS";
});

useLoginStatus.$subscribe(() => {
  reload();
});

async function reload() {
  isRouterAlive.value = false;
  await nextTick();
  isRouterAlive.value = true;
}
</script>
<style scoped>
</style>
