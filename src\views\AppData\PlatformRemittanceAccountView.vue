<template>
  <div style="display: flex; flex-wrap: wrap;">
    <h2>平台匯款帳號資訊：</h2>
    <template v-if="!firstLoading">
      <template v-if="editMode">
        <a-button size="small" type="primary" @click="cancel()">取消</a-button>
        <a-button danger size="small" style="margin-left: 8px" @click="update()">儲存</a-button>
      </template>
      <a-button v-else size="small" type="primary" @click="edit()">編輯</a-button>
    </template>
  </div>
  <a-spin v-if="firstLoading" size="large">
    <div style="width: 100%; height: 128px;"/>
  </a-spin>
  <a-descriptions v-else :column="1" bordered size="small" style="margin-bottom: 16px">
    <a-descriptions-item v-if="!editMode" label="上次更新時間">
      {{ new Date(data.updateTime).toLocaleString() }}
    </a-descriptions-item>
    <a-descriptions-item label="帳號名稱">
      <a-input v-if="editMode" v-model:value="newData.accountName"/>
      <template v-else>{{ data.accountName }}</template>
    </a-descriptions-item>
    <a-descriptions-item label="銀行代碼">
      <a-input v-if="editMode" v-model:value="newData.bankCode"/>
      <template v-else>{{ data.bankCode }}</template>
    </a-descriptions-item>
    <a-descriptions-item label="銀行帳號">
      <a-input v-if="editMode" v-model:value="newData.accountNumber"/>
      <template v-else>{{ data.accountNumber }}</template>
    </a-descriptions-item>
    <a-descriptions-item label="存摺影像">
      <div v-if="editMode">
        <template v-if="files.length == 0">
          <a-button type="primary" @click="() => setVisible(true)">預覽圖片 (未更動)</a-button>
          <a-image :preview="{ visible, onVisibleChange: setVisible }"
                   :src="data.passbookImage"
                   :style="{ display: 'none' }"
          />
        </template>
        <template v-else>
          <a-button v-if="files[files.length - 1].url === ''" disabled type="primary">預覽圖片 (上傳中)</a-button>
          <a-button v-else type="primary" @click="() => setVisible(true)">預覽圖片 (新上傳)</a-button>
          <a-image :preview="{ visible, onVisibleChange: setVisible }"
                   :src="files[files.length - 1].url"
                   :style="{ display: 'none' }"

          />
        </template>
        <UploadTool v-model:files="files" :showUploadList="false" s3-path="picture" style="margin-left: 8px"/>
      </div>
      <a-image v-else :src="data.passbookImage" height="300px"/>
    </a-descriptions-item>
  </a-descriptions>
</template>
<script lang="ts" setup>
import {createVNode, ref} from "vue";
import developmentApi from "@/api/developmentApi.ts";
import {notifyPushError} from "@/services/notify.ts";
import {ExclamationCircleOutlined} from "@ant-design/icons-vue";
import {Modal} from "ant-design-vue";
import UploadTool from "@/components/Order/Audit/UploadTool/UploadTool.vue";
import {FileListType} from "@/components/Order/Audit/UploadTool/UploadToolType.ts";

const firstLoading = ref(true);
const visible = ref<boolean>(false);
const files = ref<FileListType[]>([]);
const editMode = ref(false);
const data = ref<BankAccount>({
  updateTime: "",
  accountName: "",
  bankCode: "",
  accountNumber: "",
  passbookImage: "",
});
const newData = ref<BankAccount>({
  updateTime: "",
  accountName: "",
  bankCode: "",
  accountNumber: "",
  passbookImage: "",
});

developmentApi.platformBankAccount.get().then(res => {
  if (res.status !== 0) throw notifyPushError();
  data.value = {
    updateTime: res.updateTime,
    accountName: res.accountName,
    bankCode: res.bankCode,
    accountNumber: res.accountNumber,
    passbookImage: res.passbookImage,
  };
  firstLoading.value = false;
});

const setVisible = (value: boolean): void => {
  visible.value = value;
};

const edit = () => {
  newData.value = {
    updateTime: data.value.updateTime,
    accountName: data.value.accountName,
    bankCode: data.value.bankCode,
    accountNumber: data.value.accountNumber,
    passbookImage: data.value.passbookImage,
  };
  editMode.value = true;
};

const cancel = () => {
  editMode.value = false;
};

const update = async () => {
  Modal.confirm({
    title: '確定修改平台匯款帳號資訊',
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', {style: 'color:red;'},
        '！注意！此操作會即時更動 APP 中顯示的資料，務必先確認資料是否正確以及圖片是否可以正常預覽！注意！'),
    async onOk() {
      if (files.value.length > 0) newData.value.passbookImage = files.value[files.value.length - 1].url;
      const res = await developmentApi.platformBankAccount.set(
          newData.value.accountName,
          newData.value.bankCode,
          newData.value.accountNumber,
          newData.value.passbookImage,
      );
      if (res.status !== 0) throw notifyPushError();
      data.value = {
        updateTime: res.updateTime,
        accountName: res.accountName,
        bankCode: res.bankCode,
        accountNumber: res.accountNumber,
        passbookImage: res.passbookImage,
      };
      editMode.value = false;
    },
  });
};

interface BankAccount {
  updateTime: string;
  accountName: string;
  bankCode: string;
  accountNumber: string;
  passbookImage: string;
}
</script>
<style scoped>

</style>
