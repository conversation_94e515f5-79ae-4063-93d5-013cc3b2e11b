<template>
  <a-spin :spinning="spinning" tip="Loading...">
    <a-card>
      <h1>合約審核 ({{ listData.length === 0 ? "尚無資料" : `共${listData.length}筆` }})：</h1>
      <a-card v-for="item in listData"
              :title="item.title" size="small">
        <div style="display: flex;">
          <a-space>
            <a-image :src="item.signatureImageUrl" height="100px" width="100px"/>
          </a-space>
          <div style="padding: 16px">
            <p>簽署人姓名： {{ item.userTaiwanIdName }}</p>
            <p>簽署時間： {{ new Date(item.updateTime).toLocaleString() }} </p>
            <p>訂單客戶姓名： {{ item.orderCustomerName }}</p>
            <a-space>
              <a-button danger @click="verifyHandle(item.orderId, item.userType, false)">
                審核失敗
              </a-button>
              <a-button primary @click="verifyHandle(item.orderId, item.userType, true)">
                審核通過
              </a-button>
              <a @click="showContract(item.contentHtml, item.signatureImageUrl)">查看合約>></a>
            </a-space>
          </div>
        </div>
      </a-card>
    </a-card>
    <a-drawer
        v-model:open="drawerOpen"
        :width="600"
        class="custom-class"
        placement="right"
        root-class-name="root-class-name"
        title="合約內容"
    >
      <div v-html="drawerHtmlContent.html"></div>
      <br><br>
      <h2>簽名圖片：</h2>
      <a-image :src="drawerHtmlContent.signatureImageUrl" width="100%"/>
    </a-drawer>
  </a-spin>
</template>
<script lang="ts" setup>
import {notifyPush, notifyPushError, notifyType} from "@/services/notify.ts";
import {ref} from "vue";
import {UserGetContractItem, UserGetContractResponse} from "@/api/types/UserGetContractResponse.ts";
import {UserType} from "@/types/UserType.ts";
import orderContractApi from "@/api/orderContractApi.ts";

const spinning = ref<boolean>(true);
const listData = ref<listDataType[]>([]);
const drawerOpen = ref<boolean>(false);
const drawerHtmlContent = ref({
  html: "",
  signatureImageUrl: "",
});

refreshList();

async function refreshList(res: UserGetContractResponse | null = null) {
  if (!res) res = await orderContractApi.contractGet();
  if (!res || res.status !== 0) notifyPushError();
  else {
    listData.value = res.result.map(item => {
      const title = `【Step${item.orderType}】需審核${item.userTaiwanIdName}的${(item.userType === UserType.Customer ? "客戶" : "設計師")}端合約 (訂單ID=${item.orderId}})`;
      return {
        ...item,
        title,
      };
    });
    spinning.value = false;
  }
}

const verifyHandle = async (orderId: string, userType: UserType, isVerifyPass: boolean) => {
  const response = await orderContractApi.contractVerify(orderId, userType, isVerifyPass);
  if (!response || response.status !== 0) notifyPushError();
  else {
    notifyPush({
      type: notifyType.success,
      message: `審核(${isVerifyPass ? "通過" : "失敗"})成功`,
    });
    await refreshList(response);
  }
};

const showContract = async (contentHtml: string, signatureImageUrl: string) => {
  drawerOpen.value = true;
  drawerHtmlContent.value = {
    html: contentHtml,
    signatureImageUrl: signatureImageUrl,
  };
};

interface listDataType extends UserGetContractItem {
  title: string;
}
</script>
<style scoped>
</style>
