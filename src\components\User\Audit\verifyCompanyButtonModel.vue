<template>
  <p v-if="value.verifyStatus === VerifyStatus.Init">未上傳</p>
  <a v-else-if="value.verifyStatus === VerifyStatus.Verifying" @click="showModal">前往驗證>></a>
  <a v-else-if="value.verifyStatus === VerifyStatus.Verified" style="color: green;" @click="showModal">已驗證</a>
  <a v-else-if="value.verifyStatus === VerifyStatus.VerifyFail" style="color: red;"
     @click="showModal">驗證失敗，等待重新上傳</a>

  <a-modal v-model:open="open" :title="title" @ok="handleSuccess">
    <a-form :label-col="{style: {width: '100px'}}" :wrapper-col="{span: 16}">
      <a-form-item label="上傳時間">
        <a-input :value="new Date(value.updateTime).toLocaleString()" disabled/>
      </a-form-item>
      <a-form-item label="公司名稱">
        <a-input v-model:value="textData.companyName" :disabled="value.verifyStatus !== VerifyStatus.Verifying"/>
      </a-form-item>
      <a-form-item label="公司地點">
        <a-input v-model:value="textData.address" :disabled="value.verifyStatus !== VerifyStatus.Verifying"/>
      </a-form-item>
      <a-form-item label="公司統編">
        <a-input v-model:value="textData.unifiedBusinessNumber" :disabled="value.verifyStatus !== VerifyStatus.Verifying"/>
      </a-form-item>
      <a-form-item label="服務時間">
        <a-input v-model:value="textData.serviceTime" :disabled="value.verifyStatus !== VerifyStatus.Verifying"/>
      </a-form-item>
      <a-form-item label="公司簡介">
        <a-input v-model:value="textData.description" :disabled="value.verifyStatus !== VerifyStatus.Verifying"/>
      </a-form-item>
      <a-form-item label="公司圖示">
        <a-image :src="value.logo" width="30%"/>
      </a-form-item>
      <a-form-item label="證明文件">
        <a-image v-for="url in value.companyDocumentUrls" :src="url" width="100%"/>
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button key="submit" :disabled="buttonDisabled" :loading="failButtonLoading" danger @click="handleFail">
        審核失敗
      </a-button>
      <a-button key="submit" :disabled="buttonDisabled" :loading="successButtonLoading" type="primary"
                @click="handleSuccess">審核通過
      </a-button>
    </template>
  </a-modal>
</template>
<script lang="ts" setup>
import {computed, ref} from 'vue';
import {Company} from "@/api/types/UserAuditGetOneResponse.ts";
import {VerifyStatus} from "@/types/VerifyStatus.ts";
import {notifyPushError} from "@/services/notify.ts";
import userApi from "@/api/userApi.ts";

const title = ref<string>('Title');
const successButtonLoading = ref<boolean>(false);
const failButtonLoading = ref<boolean>(false);
const buttonDisabled = ref<boolean>(false);
const open = ref<boolean>(false);

const props = defineProps(['userId', 'modelValue']);
const emit = defineEmits(['update:modelValue']);
const value = computed({
  get() {
    return props.modelValue as Company
  },
  set(value) {
    emit('update:modelValue', value)
  }
});
const textData = ref({
  companyName: "",
  address: "",
  unifiedBusinessNumber: "",
  serviceTime: "",
  description: "",
});

const showModal = () => {
  console.log(value.value);
  switch (value.value.verifyStatus) {
    default:
      // 未知錯誤
      return;
    case VerifyStatus.Verifying:
      buttonDisabled.value = false;
      title.value = '進行審核中';
      break;
    case VerifyStatus.Verified:
      buttonDisabled.value = true;
      title.value = '已完成審核';
      break;
    case VerifyStatus.VerifyFail:
      buttonDisabled.value = true;
      title.value = '審核失敗';
      break;
  }
  textData.value = {
    companyName: value.value.companyName,
    address: value.value.address,
    unifiedBusinessNumber: value.value.unifiedBusinessNumber,
    serviceTime: value.value.serviceTime,
    description: value.value.description,
  };
  open.value = true;
};

const handleSuccess = async () => {
  title.value = '已完成審核';
  buttonDisabled.value = true;
  successButtonLoading.value = true;
  const response = await userApi.audit.companyVerify(props.userId, true,
      textData.value.companyName, textData.value.address, textData.value.unifiedBusinessNumber, textData.value.serviceTime, textData.value.description);
  if (!response || response.status !== 0) notifyPushError();
  else value.value.verifyStatus = VerifyStatus.Verified;
  successButtonLoading.value = false;
  open.value = false;
};

const handleFail = async () => {
  title.value = '審核失敗';
  buttonDisabled.value = true;
  failButtonLoading.value = true;
  const response = await userApi.audit.companyVerify(props.userId, false,
      textData.value.companyName, textData.value.address, textData.value.unifiedBusinessNumber, textData.value.serviceTime, textData.value.description);
  if (!response || response.status !== 0) notifyPushError();
  else value.value.verifyStatus = VerifyStatus.VerifyFail;
  failButtonLoading.value = false;
  open.value = false;
};
</script>

