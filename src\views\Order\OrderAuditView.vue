<template>
  <a-radio-group v-model:value="showTypeRadio" style="margin-bottom: 16px" @change="typeRadioChange">
    <a-radio-button value="customerRemittance">客戶匯款審核</a-radio-button>
    <a-radio-button value="contract">合約審核</a-radio-button>
    <a-radio-button value="designerRemittance">待匯款給設計師</a-radio-button>
    <a-radio-button value="customerInvoice">待開立發票給客戶</a-radio-button>
  </a-radio-group>
  <KeepAlive>
    <component :is="currentComponent"/>
  </KeepAlive>
</template>
<script lang="ts" setup>
import {computed, ref} from "vue";
import CustomerRemittanceVerify from "@/components/Order/Audit/CustomerRemittanceVerify.vue";
import UserContractVerify from "@/components/Order/Audit/UserContractVerify.vue";
import DesignerRemittanceSet from "@/components/Order/Audit/DesignerRemittanceSet.vue";
import {useRoute, useRouter} from "vue-router";
import CustomerInvoiceSet from "@/components/Order/Audit/CustomerInvoiceSet.vue";

const route = useRoute();
const router = useRouter();
const showTypeRadio = ref<"customerRemittance" | "contract" | "designerRemittance" | "customerInvoice">('customerRemittance');
const currentComponent = computed(() => {
  switch (showTypeRadio.value) {
    case 'customerRemittance':
      return CustomerRemittanceVerify;
    case 'contract':
      return UserContractVerify;
    case 'designerRemittance':
      return DesignerRemittanceSet;
    case 'customerInvoice':
      return CustomerInvoiceSet;
    default:
      return CustomerRemittanceVerify;
  }
});

switch (route.query.type) {
  case 'customerRemittance':
    showTypeRadio.value = 'customerRemittance';
    break;
  case 'contract':
    showTypeRadio.value = 'contract';
    break;
  case 'designerRemittance':
    showTypeRadio.value = 'designerRemittance';
    break;
  case 'customerInvoice':
    showTypeRadio.value = 'customerInvoice';
    break;
  default:
    showTypeRadio.value = 'customerRemittance';
    break;
}

function typeRadioChange() {
  const query = {type: showTypeRadio.value};
  router.replace({query});
}
</script>
<style scoped>

</style>
