<template>
  <div
    style="
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      margin: 8px 8px;
    "
  >
    <a-space>
      固定欄位：
      <a-switch
        v-model:checked="pageUserProfile.isFixed"
        @change="isFixedSwitchChange"
      />
    </a-space>
    <div v-if="tableLoading">
      <a-spin style="margin-right: 8px" />
      資料更新中...
    </div>
    <a-pagination
      v-model:current="tablePagination.current"
      v-model:page-size="tablePagination.pageSize"
      :pageSizeOptions="tablePageSizeOptions"
      :showSizeChanger="true"
      :total="currentDataCount"
    />
  </div>
  <a-table
    :columns="tableColumns"
    :data-source="tableDataSource"
    :expand-column-width="35"
    :pagination="{
      position: [],
      pageSize: tablePagination.pageSize,
      current: tablePagination.current,
    }"
    :rowKey="(record: any) => record.index"
    :scroll="{ x: '100%' }"
    :showSorterTooltip="false"
    bordered
    size="small"
    sticky
    @change="onChange"
  >
    <template #headerCell="{ column }">
      <template v-if="column.key === 'index'">
        <a-tooltip>
          <template #title>{{ column.directions }}</template>
          <info-circle-outlined class="header-icon" />
          {{ column.title }}
          <br />
          ({{ currentDataCount }})
        </a-tooltip>
      </template>
      <template v-for="item in tableColumns" v-else :key="item.key">
        <template v-if="column.key === item.key && item.directions">
          <a-tooltip>
            <template #title>{{ item.directions }}</template>
            <info-circle-outlined class="header-icon" />
            {{ item.title }}
          </a-tooltip>
        </template>
      </template>
    </template>
    <template
      #customFilterDropdown="{
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
        column,
      }"
    >
      <div style="padding: 8px">
        <a-input
          :placeholder="`Search ${column.title}`"
          :value="selectedKeys[0]"
          style="width: 188px; margin-bottom: 8px; display: block"
          @change="(e: any) => setSelectedKeys(e.target.value ? [e.target.value] : [])"
          @pressEnter="
            tableSearchBox.pressSearch(confirm, column, selectedKeys[0])
          "
        />
        <a-button
          size="small"
          style="width: 90px; margin-right: 8px"
          @click="tableSearchBox.pressReset(clearFilters)"
        >
          Reset
        </a-button>
        <a-button
          size="small"
          style="width: 90px"
          type="primary"
          @click="tableSearchBox.pressSearch(confirm, column, selectedKeys[0])"
        >
          <template #icon>
            <SearchOutlined />
          </template>
          Search
        </a-button>
      </div>
    </template>
    <template #customFilterIcon="{ filtered, column }">
      <template v-if="column.customFilterDropdown">
        <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" />
      </template>
      <template v-else>
        <filter-filled :style="{ color: filtered ? '#108ee9' : undefined }" />
      </template>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'index'">
        <a-dropdown>
          <a>
            {{ record.index }}
            <down-outlined />
          </a>
          <template #overlay>
            <a-menu>
              <a-menu-item @click="indexOperation('sendMsg', record)"
                >發送訊息</a-menu-item
              >
              <a-menu-item @click="indexOperation('undefined', record)"
                >...</a-menu-item
              >
            </a-menu>
          </template>
        </a-dropdown>
      </template>
      <template v-if="['createTime', 'refreshTime'].includes(column.key)">
        <a-tag
          v-if="new Date(record[column.key]).getTime() === 0"
          color="default"
          >無</a-tag
        >
        <a-tooltip v-else>
          <template #title>{{ formatToSecond(record[column.key]) }}</template>
          {{ formatToDate(record[column.key]) }}
        </a-tooltip>
      </template>
      <template v-if="column.key === 'userType'">
        <a-tag v-if="record.userType === UserType.Customer" color="orange">
          客戶
        </a-tag>
        <div v-else-if="record.userType === UserType.Designer">
          <designer-work v-model="record.work" :userId="record.userId" />
        </div>
        <a-tag v-else color="error"> 未知 </a-tag>
      </template>
      <template v-if="column.key === 'orderStatus'">
        <template v-if="record.verifyStatus !== 2">
          <a-tag color="orange">未通過審核</a-tag>
        </template>
        <template v-else-if="record.userType === UserType.Customer">
          <a-popover title="發單狀態切換">
            <template #content>
              <p>
                是否要將狀態從「{{
                  record.isOrderEnabled ? "可發單" : "不可發單"
                }}」切換為「{{
                  record.isOrderEnabled ? "不可發單" : "可發單"
                }}」？
              </p>
              <div style="text-align: right">
                <a-button
                  type="primary"
                  size="small"
                  @click="toggleOrderStatus(record)"
                  >確認</a-button
                >
              </div>
            </template>
            <a-tag
              v-if="record.isOrderEnabled"
              color="green"
              style="cursor: pointer"
              >可發單</a-tag
            >
            <a-tag v-else color="red" style="cursor: pointer">不可發單</a-tag>
          </a-popover>
        </template>
        <template v-else-if="record.userType === UserType.Designer">
          <a-popover title="接單狀態切換">
            <template #content>
              <p>
                是否要將狀態從「{{
                  record.isOrderEnabled ? "可接單" : "不可接單"
                }}」切換為「{{
                  record.isOrderEnabled ? "不可接單" : "可接單"
                }}」？
              </p>
              <div style="text-align: right">
                <a-button
                  type="primary"
                  size="small"
                  @click="toggleOrderStatus(record)"
                  >確認</a-button
                >
              </div>
            </template>
            <a-tag
              v-if="record.isOrderEnabled"
              color="green"
              style="cursor: pointer"
              >可接單</a-tag
            >
            <a-tag v-else color="red" style="cursor: pointer">不可接單</a-tag>
          </a-popover>
        </template>
      </template>
      <template v-if="column.key === 'username'">
        <a-tooltip v-if="(record.username?.length ?? 0) > 15">
          <template #title>{{ record.username }}</template>
          <del v-if="record.isDeleted">{{
            record.name.slice(0, 12) + "..."
          }}</del>
          <div v-else>{{ record.username.slice(0, 12) + "..." }}</div>
        </a-tooltip>
        <div v-else>
          <del v-if="record.isDeleted">{{ record.username }}</del>
          <div v-else>{{ record.username }}</div>
        </div>
      </template>
      <template v-if="column.key === 'avatar'">
        <div v-if="record.avatar.url === ''" style="height: 64px">
          <a-tag color="default" style="margin-top: 24px">無</a-tag>
        </div>
        <a-image v-else :src="record.avatar.url" height="64px" />
      </template>
      <template v-if="column.key === 'email'">
        {{ record.email.address }}
      </template>
      <template v-if="column.key === 'gender'">
        <a-tag v-if="record.gender === Gender.Male" color="blue"> 男性 </a-tag>
        <a-tag v-else-if="record.gender === Gender.Female" color="pink">
          女性
        </a-tag>
        <a-tag v-else-if="record.gender === Gender.Private" color="purple">
          保密
        </a-tag>
        <a-tag v-else> 未填 </a-tag>
      </template>
      <template v-if="column.key === 'taiwanId'">
        <a-tag v-if="record.taiwanId.verifyStatus === VerifyStatus.Init">
          未填寫
        </a-tag>
        <a-popover v-else title="身份證資訊">
          <template #content>
            資料更新時間：{{
              new Date(record.taiwanId.updateTime).toLocaleString()
            }}
            <br />
            身分證字號：{{
              record.taiwanId.idNumber === "" ? "無" : record.taiwanId.idNumber
            }}
            <br />
            身分證正反面：
            <div
              style="
                display: flex;
                justify-content: space-around;
                margin-top: 8px;
              "
            >
              <a-image :src="record.taiwanId.frontSideUrl" height="200px" />
              <span style="width: 16px" />
              <a-image :src="record.taiwanId.backSideUrl" height="200px" />
            </div>
          </template>
          <a-tag
            v-if="record.taiwanId.verifyStatus === VerifyStatus.Verifying"
            color="blue"
          >
            審核中
          </a-tag>
          <a-tag
            v-else-if="record.taiwanId.verifyStatus === VerifyStatus.Verified"
            color="green"
          >
            已審核
          </a-tag>
          <a-tag
            v-else-if="record.taiwanId.verifyStatus === VerifyStatus.VerifyFail"
            color="red"
          >
            審核失敗
          </a-tag>
        </a-popover>
      </template>
      <template v-if="column.key === 'company'">
        <a-tag v-if="!record.company"> 無 </a-tag>
        <a-tag v-else-if="record.company.verifyStatus === VerifyStatus.Init">
          未填寫
        </a-tag>
        <a-popover v-else title="公司資訊">
          <template #content>
            資料更新時間：{{
              new Date(record.company.updateTime).toLocaleString()
            }}
            <br />
            公司名稱：{{ record.company.companyName }}
            <br />
            公司統編：{{ record.company.unifiedBusinessNumber }}
            <br />
            公司證明文件：
            <div
              v-for="img in record.company.companyDocumentUrls"
              style="margin-top: 8px"
            >
              <a-image :src="img" height="200px" />
            </div>
          </template>
          <a-tag
            v-if="record.company.verifyStatus === VerifyStatus.Verifying"
            color="blue"
          >
            審核中
          </a-tag>
          <a-tag
            v-else-if="record.company.verifyStatus === VerifyStatus.Verified"
            color="green"
          >
            已審核
          </a-tag>
          <a-tag
            v-else-if="record.company.verifyStatus === VerifyStatus.VerifyFail"
            color="red"
          >
            審核失敗
          </a-tag>
        </a-popover>
      </template>
      <template v-if="column.key === 'bankAccount'">
        <a-tag v-if="!record.bankAccount"> 無 </a-tag>
        <a-tag v-else-if="!record.bankAccount.isSet"> 未設定 </a-tag>
        <a-popover v-else title="匯款帳戶資訊">
          <template #content>
            資料更新時間：{{
              new Date(record.bankAccount.updateTime).toLocaleString()
            }}
            <br />
            戶名：{{ record.bankAccount.accountName }}
            <br />
            銀行代碼：{{ record.bankAccount.bankCode }}
            <br />
            賬戶號碼：{{ record.bankAccount.accountNumber }}
          </template>
          <a-tag v-if="record.bankAccount.isSet" color="green"> 已設定 </a-tag>
        </a-popover>
      </template>
      <template v-if="column.key === 'deviceInfo'">
        <img
          v-if="record.deviceInfo.deviceType === DeviceTypeEnum.IOS"
          alt="iOS"
          src="@/assets/ios_icon.png"
          width="32"
        />
        <img
          v-else-if="record.deviceInfo.deviceType === DeviceTypeEnum.Android"
          alt="Android"
          src="@/assets/android_icon.png"
          width="32"
        />
        <img
          v-else-if="record.deviceInfo.deviceType === DeviceTypeEnum.Web"
          alt="Web"
          src="@/assets/web_icon.png"
          width="32"
        />
        <a-tag
          v-else-if="record.deviceInfo.deviceType === DeviceTypeEnum.Unknown"
          >未知</a-tag
        >
        <a-tag v-else color="error">錯誤</a-tag>
      </template>
    </template>
    <template #expandedRowRender="{ record }">
      UserID：{{ record.userId }}<br />
      Token：{{ record.authToken }}
    </template>
  </a-table>
</template>
<script lang="ts" setup>
import {
  DownOutlined,
  FilterFilled,
  InfoCircleOutlined,
  SearchOutlined,
} from "@ant-design/icons-vue";
import { onMounted, ref } from "vue";
import { TableProps } from "ant-design-vue";
import {
  formatToDate,
  formatToSecond,
} from "@/composables/User/Profile/dateTool.ts";
import userApi from "@/api/userApi.ts";
import { tableColumnsData } from "@/composables/User/Profile/columnConfig.ts";
import {
  DeviceTypeEnum,
  UserProfileListItem,
} from "@/api/types/UserProfileListResponse.ts";
import { UserType } from "@/types/UserType.ts";
import { Gender } from "@/types/Gender.ts";
import { notifyPush, notifyType } from "@/services/notify.ts";
import { userProfileStore } from "@/stores/User/Profile/userProfileStore.ts";
import DesignerWork from "@/components/User/Profile/designerWork.vue";
import { VerifyStatus } from "@/types/VerifyStatus.ts";

const tablePageSizeOptions = ["5", "10", "20", "50", "100"];
const pageUserProfile = userProfileStore();

const tableDataSource = ref<UserProfileListItem[]>(pageUserProfile.data);
const tableColumns = ref(tableColumnsData);
const tableLoading = ref<boolean>(true);
const tablePagination = ref({
  pageSize: 10,
  current: 1,
});
const currentDataCount = ref(pageUserProfile.count);
const tableSearchBox = ref({
  text: "",
  key: "",
  pressSearch: (confirm: any, column: any, text: string) => {
    confirm();
    tableSearchBox.value.key = column.key;
    tableSearchBox.value.text = text;
  },
  pressReset: (clearFilters: any) => {
    clearFilters({ confirm: true });
    tableSearchBox.value.text = "";
  },
});

const indexOperation = (action: string, record: UserProfileListItem) => {
  switch (action) {
    case "sendMsg":
      notifyPush({
        type: notifyType.info,
        message: "功能開發中",
        description: record.userId,
      });
      break;
    default:
      notifyPush({
        type: notifyType.info,
        message: "功能尚未開發",
        description: "需增加功能請洽詢開發人員",
      });
  }
};

const isFixedSwitchChange = (value: boolean) => {
  tableColumns.value
    .filter((column) => column.fixed !== undefined)
    .forEach((column) => (column.fixed = value ? "left" : false));
};

const onChange: TableProps<UserProfileListItem>["onChange"] = (
  _pagination,
  _filters,
  _sorter,
  extra
) => {
  currentDataCount.value = extra.currentDataSource.length;
};

const toggleOrderStatus = async (record: UserProfileListItem) => {
  try {
    tableLoading.value = true;
    const newStatus = !record.isOrderEnabled;

    const response = await userApi.profile.IsOrderEnabledSet(
      record.userId,
      newStatus
    );

    if (response.status === 0) {
      // 更新本地数据
      record.isOrderEnabled = newStatus;

      notifyPush({
        type: notifyType.success,
        message: "狀態更新成功",
      });
    } else {
      notifyPush({
        type: notifyType.error,
        message: "狀態更新失敗",
        description: response.msg || response.errMsg || "請稍後再試",
      });
    }
  } catch (error) {
    console.error("Error toggling order status:", error);
    notifyPush({
      type: notifyType.error,
      message: "狀態更新失敗",
      description: "網路錯誤，請稍後再試",
    });
  } finally {
    tableLoading.value = false;
  }
};

onMounted(async () => {
  isFixedSwitchChange(pageUserProfile.isFixed);
  const data = (await userApi.profile.list()).result;
  pageUserProfile.update(data);
  tableDataSource.value = pageUserProfile.data;
  currentDataCount.value = pageUserProfile.count;
  tableLoading.value = false;
});
</script>
<style>
.header-icon {
  color: gray;
  margin-right: 2px;
}
</style>
