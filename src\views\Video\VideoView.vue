<template>
  <div>
    <a-button style="margin: 0 16px 16px 0;" type="primary" @click="create()">
      從 Youtube 匯入新影片
    </a-button>
    <a-radio-group v-model:value="videoType" style="margin: 0 16px 16px 0;" @change="refresh">
      <a-radio-button :value="VideoType.None">全部</a-radio-button>
      <a-radio-button :value="VideoType.DecorationMaster">裝潢大師</a-radio-button>
      <a-radio-button :value="VideoType.StorageExpert">收納專家</a-radio-button>
      <a-radio-button :value="VideoType.FengShuiFamily">風水世家</a-radio-button>
      <a-radio-button :value="VideoType.BrandGuru">品牌達人</a-radio-button>
    </a-radio-group>
    <a-input-search
        v-model:value="search"
        enter-button="搜尋"
        placeholder="影片搜尋"
        style="width: 300px; margin: 0 64px 16px 0;"
        @search="refresh()"
    />
    <a-pagination
        v-model:current="current"
        v-model:page-size="pageSize"
        :show-total="(total: number, range: number[]) => `${range[0]}-${range[1]} of ${total} items`"
        :total="totalCount"
        showSizeChanger
        style="width: fit-content; display: initial;"
        @change="refresh"
    />
  </div>
  <a-spin :spinning="initLoading" tip="Loading...">
    <div :style="{margin: videosShow.length === 0 ? '100px' : 0}"
         style="display: flex; flex-wrap: wrap; justify-content: space-around;">
      <VideoCard v-for="video in videosShow" :key="video.key" v-model="video.value"/>
    </div>
  </a-spin>

  <a-modal v-model:open="createMode" cancel-text="取消" ok-text="確認" title="從 Youtube 匯入新影片" @ok="createOk">
    <a-form :label-col="{style: {width: '100px'}}" :wrapper-col="{span: 16}">
      <a-form-item label="Youtube ID">
        <a-input v-model:value="inputYoutubeId"/>
      </a-form-item>
      <a-form-item label="影片類型">
        <a-radio-group v-model:value="videoStlType">
          <a-radio :value="VideoType.DecorationMaster">裝潢大師</a-radio>
          <a-radio :value="VideoType.StorageExpert">收納專家</a-radio>
          <a-radio :value="VideoType.FengShuiFamily">風水世家</a-radio>
          <a-radio :value="VideoType.BrandGuru">品牌達人</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import videoApi from "@/api/videoApi.ts";
import {VideoType, VideoTypeReq} from "@/types/VideoType.ts";
import {Video, VideoStatus} from "@/api/types/VideoGetListResponse.ts";
import {computed, ref} from "vue";
import VideoCard from "@/components/Video/videoCard.vue";
import {notifyPush, notifyPushError, notifyType} from "@/services/notify.ts";

const search = ref<string>("");
const initLoading = ref<boolean>(true);
const videos = ref<Video[]>([]);
const videosShow = computed((): { key: string; value: Video }[] => {
  return videos.value
      .filter(e => e.status !== VideoStatus.OffShelf)
      .map(e => ({key: e.videoId, value: e}));
});
const videoType = ref<VideoTypeReq>(VideoTypeReq.All);
const createMode = ref<boolean>(false);
const inputYoutubeId = ref<string>('');
const youtubeId = computed(() => extractYouTubeID(inputYoutubeId.value));
const videoStlType = ref<VideoType>(VideoType.None);
const current = ref<number>(1);
const pageSize = ref<number>(10);
const totalCount = ref<number>(0);

refresh();

async function create() {
  createMode.value = true;
  inputYoutubeId.value = '';
  videoStlType.value = VideoType.None;
}

async function createOk() {
  if (!youtubeId.value) {
    notifyPushError();
    return;
  }
  const response = await videoApi.update(youtubeId.value, videoStlType.value);
  if (response.status !== 0) throw notifyPushError();
  else {
    notifyPush({type: notifyType.success, message: "新增影片完成"});
    await refresh();
    createMode.value = false;
  }
}

async function refresh() {
  initLoading.value = true;
  const response = await videoApi.getList(
      (current.value - 1) * pageSize.value,
      current.value * pageSize.value,
      search.value, videoType.value);
  if (response.status !== 0) throw notifyPushError();
  videos.value = response.videos;
  totalCount.value = response.count;
  initLoading.value = false;
}

function extractYouTubeID(url: string): string {
  // 直接檢查是否為純ID格式
  if (/^[a-zA-Z0-9_-]{11}$/.test(url)) return url;

  // 解析標準YouTube網址和短網址
  const regexPatterns = [
    /\?v=([a-zA-Z0-9_-]{11})/, // 標準格式
    /youtu\.be\/([a-zA-Z0-9_-]{11})/, // 短網址格式
    /youtube\.com\/shorts\/([a-zA-Z0-9_-]{11})/, // 短片格式
  ];

  for (let pattern of regexPatterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return '';
}
</script>
<style scoped>

</style>
