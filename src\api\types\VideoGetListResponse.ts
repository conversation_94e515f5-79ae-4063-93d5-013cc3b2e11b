import {ResponseBase} from "@/api/types/ResponseBase.ts";
import {VideoType} from "@/types/VideoType.ts";

export interface VideoGetListResponse extends ResponseBase {
  videos: Video[];
  count: number;
}

export interface Video {
  videoId: string;
  createTime: string;
  refreshTime: string;
  title: string;
  description: string;
  type: VideoType;
  tags: string[];
  isShort: boolean;
  youtubeId: string;
  publishedAt: string;
  duration: number;
  thumbnails: {
    default: Thumbnail;
    high: Thumbnail;
    maxres: Thumbnail;
    medium: Thumbnail;
    standard: Thumbnail;
  };
  likes: number;
  isLiked: boolean;
  views: number;
  status: VideoStatus;
}

export interface Thumbnail {
  height: number;
  width: number;
  url: string;
}

export enum VideoStatus {
  /**
   * 已下架/刪除
   */
  OffShelf = -1,
  /**
   * 未刊登
   */
  NotPublished = 0,
  /**
   * 已刊登
   */
  Published = 1,
}
