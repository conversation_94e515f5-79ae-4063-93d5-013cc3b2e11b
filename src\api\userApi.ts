import { postApiByToken } from "@/api/baseApi";
import { ResponseBase } from "@/api/types/ResponseBase";
import { Gender } from "@/types/Gender.ts";
import { UserAuditGetOneResponse } from "@/api/types/UserAuditGetOneResponse.ts";
import { UserAuditGetListResponse } from "@/api/types/UserAuditGetListResponse.ts";
import { UserType } from "@/types/UserType.ts";
import { UserProfileListResponse } from "@/api/types/UserProfileListResponse.ts";
import { DesignerWorkType } from "@/types/DesignerWorkType.ts";

export default {
  profile: {
    list: async () =>
      await postApiByToken<UserProfileListResponse>("Ms/User/Profile/List"),

    updateWork: async (
      userId: string,
      set: boolean,
      workType: DesignerWorkType
    ) =>
      await postApiByToken<ResponseBase>("Ms/User/Profile/Update/Work", {
        userId,
        set,
        workType,
      }),

    IsOrderEnabledSet: async (userId: string, set: boolean) =>
      await postApiByToken<ResponseBase>("Ms/User/Profile/IsOrderEnabled/Set", {
        userId,
        set,
      }),
  },
  audit: {
    getList: async () =>
      await postApiByToken<UserAuditGetListResponse>("Ms/User/Audit/GetList"),

    getOne: async (userId: string) =>
      await postApiByToken<UserAuditGetOneResponse>("Ms/User/Audit/GetOne", {
        userId,
      }),

    taiwanIdVerify: async (
      userId: string,
      set: boolean,
      userType: UserType,
      gender: Gender,
      idNumber: string
    ) =>
      await postApiByToken<ResponseBase>("Ms/User/Audit/TaiwanId/Verify", {
        userId,
        set,
        userType,
        gender,
        idNumber,
      }),

    companyVerify: async (
      userId: string,
      set: boolean,
      companyName: string,
      address: string,
      unifiedBusinessNumber: string,
      serviceTime: string,
      description: string
    ) =>
      await postApiByToken<ResponseBase>("Ms/User/Audit/Company/Verify", {
        userId,
        set,
        companyName,
        address,
        unifiedBusinessNumber,
        serviceTime,
        description,
      }),
  },
};
