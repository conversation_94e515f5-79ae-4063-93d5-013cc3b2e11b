import {Component, FunctionalComponent} from "vue";

export type RouterMapType =
  RouterRecordWithComponent |
  RouterRecordWithRedirect |
  RouterRecordWithBody;

export interface RouterRecordWithComponent {
  key: string;
  name: string;
  path: string;
  icon: FunctionalComponent;
  type: "top" | "bottom";
  component: Component;
}

export interface RouterRecordWithRedirect {
  key: string;
  name: string;
  path: string;
  icon: FunctionalComponent;
  type: "top" | "bottom";
  redirect: string;
}

export interface RouterRecordWithBody {
  key: string;
  name: string;
  icon: FunctionalComponent;
  type: "body";
  body: RouteRecordBody[];
}

export type RouteRecordBody = RouteRecordBodyWithComponent | RouteRecordBodyWithRedirect;

export interface RouteRecordBodyWithComponent {
  key: string;
  name: string;
  path: string;
  component: Component;
}

export interface RouteRecordBodyWithRedirect {
  key: string;
  name: string;
  path: string;
  redirect: string;
}
