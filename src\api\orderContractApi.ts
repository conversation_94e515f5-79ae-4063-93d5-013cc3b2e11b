import {postApiByToken} from "@/api/baseApi";
import {CustomerRemittanceGetResponse} from "@/api/types/CustomerRemittanceGetResponse.ts";
import {UserGetContractResponse} from "@/api/types/UserGetContractResponse.ts";
import {DesignerRemittanceGetResponse} from "@/api/types/DesignerRemittanceGetResponse.ts";
import {UserType} from "@/types/UserType.ts";
import {CustomerInvoiceGetResponse, IssueResult} from "@/api/types/CustomerInvoiceGetResponse.ts";
import {OrderInfoGetListResponse} from "@/api/types/OrderInfoGetListResponse.ts";
import {OrderType} from "@/types/OrderType.ts";

export default {
  info: {
    getList: async (skip: number, limit: number, orderType: OrderType) =>
      await postApiByToken<OrderInfoGetListResponse>(
        'Ms/Order/Info/GetList',
        {skip, limit, orderType}),
  },

  contractGet: async (): Promise<UserGetContractResponse | null> =>
    await postApiByToken('Ms/Contract/Get'),

  contractVerify: async (orderId: string, userType: UserType, isVerifyPass: boolean): Promise<UserGetContractResponse | null> =>
    await postApiByToken('Ms/Contract/Verify', {orderId, userType, isVerifyPass}),

  remittanceCustomerGet: async (): Promise<CustomerRemittanceGetResponse | null> =>
    await postApiByToken('Ms/Remittance/Customer/Get'),

  remittanceCustomerVerify: async (remittanceId: string, isVerifyPass: boolean): Promise<CustomerRemittanceGetResponse | null> =>
    await postApiByToken('Ms/Remittance/Customer/Verify', {remittanceId, isVerifyPass}),

  remittancePlatformGet: async (): Promise<DesignerRemittanceGetResponse | null> =>
    await postApiByToken('Ms/Remittance/Platform/Get'),

  remittancePlatformSet: async (remittanceId: string, proofImageUrls: string[]): Promise<DesignerRemittanceGetResponse | null> =>
    await postApiByToken('Ms/Remittance/Platform/Set', {remittanceId, proofImageUrls}),

  invoiceGet: async (): Promise<CustomerInvoiceGetResponse | null> =>
    await postApiByToken('Ms/Invoice/Get'),

  invoiceSet: async (invoiceId: string, issueResults: IssueResult[]): Promise<CustomerInvoiceGetResponse | null> =>
    await postApiByToken('Ms/Invoice/Set', {invoiceId, issueResults}),
}
