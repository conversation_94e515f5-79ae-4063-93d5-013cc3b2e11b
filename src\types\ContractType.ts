export enum ContractType {
  /** Step2設計師合約 */
  Step2Designer = 1,
  /** Step2客戶合約 */
  Step2Customer = 2,
  /** Step3普通設計師合約 */
  Step3NormalDesigner = 3,
  /** Step3普通客戶合約 */
  Step3NormalCustomer = 4,
  /** Step3接續設計師合約 */
  Step3ContinueDesigner = 5,
  /** Step3接續客戶合約 */
  Step3ContinueCustomer = 6,
}
export const ContractTypeDescriptions: Record<ContractType, string> = {
  [ContractType.Step2Designer]: "Step2設計師合約",
  [ContractType.Step2Customer]: "Step2客戶合約",
  [ContractType.Step3NormalDesigner]: "Step3普通訂單設計師合約",
  [ContractType.Step3NormalCustomer]: "Step3普通訂單客戶合約",
  [ContractType.Step3ContinueDesigner]: "Step3接續訂單設計師合約",
  [ContractType.Step3ContinueCustomer]: "Step3接續訂單客戶合約",
};