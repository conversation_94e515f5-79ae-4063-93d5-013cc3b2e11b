<template>
  <a-card style="width: 320px; margin: 8px">
    <template #cover>
      <youtube-embed :height="180" :video-id="model!.youtubeId" :width="320"/>
    </template>
    <template #actions>
      <div @click="changeState">
        <template v-if="model!.status === VideoStatus.Published"
                  style="color: chartreuse; background-color: chartreuse">
          <EyeOutlined style="color: limegreen"/>
          <p style="display: inline; color: limegreen"> 已刊登</p>
        </template>
        <template v-else>
          <EyeInvisibleOutlined style="color: orange"/>
          <p style="display: inline; color: orange"> 未刊登</p>
        </template>
      </div>
      <div @click="showEditModal">
        <edit-outlined/>
        編輯
      </div>
      <div @click="showDeleteConfirm">
        <DeleteOutlined/>
        刪除
      </div>
    </template>
    <a-card-meta :title="model!.title" style="margin-bottom: 8px"/>
    <div style="height: 64px; overflow: hidden;">
      {{ model!.description }}
    </div>
  </a-card>

  <a-modal v-model:open="editMode" cancel-text="返回" ok-text="儲存" title="編輯影片資訊" @ok="editOk">
    <a-form :label-col="{style: {width: '100px'}}" :wrapper-col="{span: 16}">
      <a-form-item label="Youtube ID">
        <a-space>
          <a-input :value="model!.youtubeId" disabled/>
          <a-button :disabled="refreshOneLoading" type="primary" @click="refreshOne">
            <loading-outlined v-if="refreshOneLoading"/>
            重置內容
          </a-button>
        </a-space>
      </a-form-item>
      <a-form-item label="影片標題">
        <a-input v-model:value="editData.title"/>
      </a-form-item>
      <a-form-item label="影片簡介">
        <a-textarea v-model:value="editData.description"/>
      </a-form-item>
      <a-form-item label="影片類型">
        <a-radio-group v-model:value="editData.type">
          <a-radio :value="VideoType.DecorationMaster">裝潢大師</a-radio>
          <a-radio :value="VideoType.StorageExpert">收納專家</a-radio>
          <a-radio :value="VideoType.FengShuiFamily">風水世家</a-radio>
          <a-radio :value="VideoType.BrandGuru">品牌達人</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="影片標籤">
        <template v-if="model!.tags.length === 0">
          <a-tag>無</a-tag>
        </template>
        <a-tag v-for="tag in model!.tags" v-else>{{ tag }}</a-tag>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import {Video, VideoStatus} from "@/api/types/VideoGetListResponse.ts";
import YoutubeEmbed from "@/components/Video/youtubeEmbed.vue";
import {
  DeleteOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  LoadingOutlined,
} from '@ant-design/icons-vue';
import videoApi from "@/api/videoApi.ts";
import {createVNode, ref} from "vue";
import {notifyPush, notifyPushError, notifyType} from "@/services/notify.ts";
import {Modal} from "ant-design-vue";
import {VideoType} from "@/types/VideoType.ts";

const model = defineModel<Video>()
const editMode = ref<boolean>(false);
const editData = ref({
  title: '',
  description: '',
  type: VideoType.None,
});
const refreshOneLoading = ref<boolean>(false);

function showEditModal() {
  editData.value.title = model.value!.title;
  editData.value.description = model.value!.description;
  editData.value.type = model.value!.type;
  editMode.value = true;
}

async function refreshOne() {
  refreshOneLoading.value = true;
  const response = await videoApi.update(model.value!.youtubeId, model.value!.type);
  if (response.status !== 0) throw notifyPushError();
  model.value!.title = response.video.title;
  model.value!.description = response.video.description;
  model.value!.type = response.video.type;
  editData.value.title = response.video.title;
  editData.value.description = response.video.description;
  editData.value.type = response.video.type;
  refreshOneLoading.value = false;
}

async function editOk() {
  const response = await videoApi.editInfo(model.value!.videoId, editData.value.title, editData.value.description, editData.value.type);
  if (response.status !== 0) throw notifyPushError();
  model.value!.title = response.video.title;
  model.value!.description = response.video.description;
  model.value!.type = response.video.type;
  notifyPush({
    type: notifyType.success,
    message: '影片編輯成功',
  });
  editMode.value = false;
}

const showDeleteConfirm = () => {
  Modal.confirm({
    title: '確認刪除此影片?',
    icon: createVNode(ExclamationCircleOutlined),
    content: model.value!.title,
    okText: '確認',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      const response = await videoApi.editStatus(model.value!.videoId, VideoStatus.OffShelf);
      if (response.status !== 0) throw notifyPushError();
      model.value!.status = response.video.status;
      notifyPush({
        type: notifyType.success,
        message: '影片刪除成功',
      });
    },
  });
};

async function changeState() {
  const response = await videoApi.editStatus(model.value!.videoId,
      model.value!.status === VideoStatus.Published ? VideoStatus.NotPublished : VideoStatus.Published);
  if (response.status !== 0) throw notifyPushError();
  model.value!.status = response.video.status;
  notifyPush({
    type: notifyType.success,
    message: '影片狀態已變更',
  });
}
</script>
<style scoped>

</style>
