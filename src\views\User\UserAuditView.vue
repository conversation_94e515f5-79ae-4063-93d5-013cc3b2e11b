<template>
  <a-radio-group v-model:value="showTypeRadio" @change="typeRadioChange" style="margin-bottom: 16px">
    <a-radio-button value="all">全部</a-radio-button>
    <a-radio-button value="customer">客戶</a-radio-button>
    <a-radio-button value="designer">設計師</a-radio-button>
  </a-radio-group>
  <div v-if="initLoading" style="text-align: center;">
    <a-spin spinning tip="Loading..." style="width: fit-content; margin: 100px"/>
  </div>
  <a-spin v-for="item in showData" v-else :spinning="item.loading" tip="Loading...">
    <AuditCard :key="item.data.userId" style="margin-bottom: 8px" v-bind="item.data" @refresh="refreshOneData"/>
  </a-spin>
</template>
<script lang="ts" setup>
import {computed, ref} from "vue";
import userApi from "@/api/userApi.ts";
import {notifyPushError} from "@/services/notify.ts";
import {UserAuditGetItem} from "@/api/types/UserAuditGetOneResponse.ts";
import {UserType} from "@/types/UserType.ts";
import AuditCard from "@/components/User/Audit/auditCard.vue";
import {useRoute, useRouter} from "vue-router";

const route = useRoute();
const router = useRouter();
const initLoading = ref<boolean>(true);
const showTypeRadio = ref<"all" | "customer" | "designer">('all');
const listData = ref<ListDataType[]>([]);
const customerData = computed(() => listData.value.filter(e => e.data.userType === UserType.Customer));
const designerData = computed(() => listData.value.filter(e => e.data.userType === UserType.Designer));
const showData = computed((): ListDataType[] => {
  if (showTypeRadio.value === 'all') return listData.value;
  if (showTypeRadio.value === 'customer') return customerData.value;
  if (showTypeRadio.value === 'designer') return designerData.value;
  return [];
});

refreshData();
switch (route.query.type) {
  case 'customer':
    showTypeRadio.value = 'customer';
    break;
  case 'designer':
    showTypeRadio.value = 'designer';
    break;
  default:
    showTypeRadio.value = 'all';
    break;
}

function typeRadioChange() {
  const query = {type: showTypeRadio.value};
  router.replace({query});
}

async function refreshOneData(userId: string, loading: "start" | "end") {
  let item = listData.value.find(e => e.data.userId === userId);
  if (!item) return;
  if (loading === "start") item.loading = true;
  else {
    const res = await userApi.audit.getOne(userId);
    item.data = res.result;
    item.loading = false;
  }
}

async function refreshData() {
  const res = await userApi.audit.getList();
  if (res.status !== 0) notifyPushError();
  else listData.value = res.result.map(e => ({loading: false, data: e}));
  initLoading.value = false;
}

interface ListDataType {
  loading: boolean;
  data: UserAuditGetItem;
}
</script>

<style scoped>

</style>
