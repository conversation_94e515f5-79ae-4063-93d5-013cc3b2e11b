import {HubConnection, HubConnectionBuilder, LogLevel} from "@microsoft/signalr";

export class ChatHubClass {
  public readonly hubUrl: string;
  private _hubConnection: HubConnection | null = null;
  private oncloseCallback: ((error?: (Error | undefined)) => void) | null = null;

  constructor(hub: string) {
    this.hubUrl = hub;
  }

  private _isConnected: boolean = false;

  public get isConnected() {
    return this._hubConnection && this._isConnected;
  }

  onclose(callback: (error?: (Error | undefined)) => void) {
    this.oncloseCallback = callback;
    if (!this.isConnected) throw new Error("SignalR Not connected");
    (this._hubConnection as HubConnection).onclose(async () => {
      this._isConnected = false;
      if (this.oncloseCallback !== null)
        this.oncloseCallback();
    });
  }

  async connection() {
    if (this.isConnected) await this.disconnect();
    this._hubConnection = new HubConnectionBuilder()
      .withUrl(this.hubUrl)
      .configureLogging(LogLevel.Information)
      .build();
    try {
      await this._hubConnection.start();
      this._isConnected = true;
      this.onclose(() => {
      });
    } catch (err) {
      this._isConnected = false;
      throw err;
    }
  }

  async disconnect() {
    if (this.isConnected)
      await (this._hubConnection as HubConnection).stop();
    this._isConnected = false;
  }

  public get connectionId() {
    if (!this.isConnected) return "";
    return (this._hubConnection as HubConnection).connectionId ?? "";
  }

  on(methodName: string, newMethod: (...args: any[]) => any): void {
    if (!this.isConnected) throw new Error("SignalR Not connected");
    (this._hubConnection as HubConnection).on(methodName, newMethod);
  }

  invoke(methodName: string, ...args: any[]): Promise<any> {
    if (!this.isConnected) throw new Error("SignalR Not connected");
    return (this._hubConnection as HubConnection).invoke(methodName, ...args);
  }
}
