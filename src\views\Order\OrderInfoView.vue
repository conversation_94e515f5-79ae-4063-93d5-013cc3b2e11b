<template>
  <div
    style="
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      margin: 8px 8px;
    "
  >
    <a-radio-group
      v-model:value="orderType"
      style="margin-bottom: 16px"
      @change="refresh"
    >
      <a-radio-button :value="OrderType.Measure">丈量訂單</a-radio-button>
      <a-radio-button :value="OrderType.Design">設計訂單</a-radio-button>
      <a-radio-button :value="OrderType.Construction">裝潢訂單</a-radio-button>
    </a-radio-group>
    <div v-if="tableLoading">
      <a-spin style="margin-right: 8px" />
      資料更新中...
    </div>
    <a-pagination
      v-model:current="tablePagination.current"
      v-model:page-size="tablePagination.pageSize"
      :pageSizeOptions="tablePageSizeOptions"
      :showSizeChanger="true"
      :total="currentDataCount"
      @change="refresh"
    />
  </div>
  <a-table
    :columns="tableColumns"
    :data-source="tableDataSource"
    :expand-column-width="35"
    :pagination="{
      position: [],
      pageSize: tablePagination.pageSize,
      current: tablePagination.current,
    }"
    :rowKey="(record: any) => record.index"
    :scroll="{ x: '100%' }"
    :showSorterTooltip="false"
    bordered
    size="small"
    sticky
    @change="onChange"
  >
    <template #headerCell="{ column }">
      <template v-if="column.key === 'index'">
        <a-tooltip>
          <template #title>{{ column.directions }}</template>
          <info-circle-outlined class="header-icon" />
          {{ column.title }}
          <br />
          ({{ currentDataCount }})
        </a-tooltip>
      </template>
      <template v-for="item in tableColumns" v-else :key="item.key">
        <template v-if="column.key === item.key && item.directions">
          <a-tooltip>
            <template #title>{{ item.directions }}</template>
            <info-circle-outlined class="header-icon" />
            {{ item.title }}
          </a-tooltip>
        </template>
      </template>
    </template>
    <template
      #customFilterDropdown="{
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
        column,
      }"
    >
      <div style="padding: 8px">
        <a-input
          :placeholder="`Search ${column.title}`"
          :value="selectedKeys[0]"
          style="width: 188px; margin-bottom: 8px; display: block"
          @change="(e: any) => setSelectedKeys(e.target.value ? [e.target.value] : [])"
          @pressEnter="
            tableSearchBox.pressSearch(confirm, column, selectedKeys[0])
          "
        />
        <a-button
          size="small"
          style="width: 90px; margin-right: 8px"
          @click="tableSearchBox.pressReset(clearFilters)"
        >
          Reset
        </a-button>
        <a-button
          size="small"
          style="width: 90px"
          type="primary"
          @click="tableSearchBox.pressSearch(confirm, column, selectedKeys[0])"
        >
          <template #icon>
            <SearchOutlined />
          </template>
          Search
        </a-button>
      </div>
    </template>
    <template #customFilterIcon="{ filtered, column }">
      <template v-if="column.customFilterDropdown">
        <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" />
      </template>
      <template v-else>
        <filter-filled :style="{ color: filtered ? '#108ee9' : undefined }" />
      </template>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'index'">
        <a-dropdown>
          <a>
            {{ record.index }}
            <down-outlined />
          </a>
          <template #overlay>
            <a-menu>
              <a-menu-item @click="indexOperation('undefined', record)"
                >尚未實作</a-menu-item
              >
            </a-menu>
          </template>
        </a-dropdown>
      </template>
      <template v-if="column.key === 'address'">
        {{ record[column.key].fullName }}
      </template>
      <template v-if="column.key === 'orderType'">
        <a-tag v-if="record[column.key] === OrderType.Measure" color="blue"
          >丈量</a-tag
        >
        <a-tag v-else-if="record[column.key] === OrderType.Design" color="green"
          >設計</a-tag
        >
        <a-tag
          v-else-if="record[column.key] === OrderType.Construction"
          color="orange"
          >裝潢</a-tag
        >
      </template>
      <template v-if="column.key === 'status'">
        {{
          getOrderStatusName(
            record.orderType,
            record.orderType === OrderType.Measure
              ? record.measureItem.status
              : record.orderType === OrderType.Design
              ? record.designItem.status
              : record.constructionItem.status
          )
        }}
      </template>
      <template v-if="['createTime', 'refreshTime'].includes(column.key)">
        <a-tag
          v-if="new Date(record[column.key]).getTime() === 0"
          color="default"
          >無</a-tag
        >
        <a-tooltip v-else>
          <template #title>{{ formatToSecond(record[column.key]) }}</template>
          {{ formatToDate(record[column.key]) }}
        </a-tooltip>
      </template>
    </template>
    <template #expandedRowRender="{ record }">
      {{ record }}
    </template>
  </a-table>
</template>
<script lang="ts" setup>
import {
  DownOutlined,
  FilterFilled,
  InfoCircleOutlined,
  SearchOutlined,
} from "@ant-design/icons-vue";
import { onMounted, ref } from "vue";
import { TableProps } from "ant-design-vue";
import {
  formatToDate,
  formatToSecond,
} from "@/composables/User/Profile/dateTool.ts";
import { tableColumnsData } from "@/composables/Order/Info/columnConfig.ts";
import { UserProfileListItem } from "@/api/types/UserProfileListResponse.ts";
import { notifyPush, notifyType } from "@/services/notify.ts";
import orderContractApi from "@/api/orderContractApi.ts";
import { OrderType } from "@/types/OrderType.ts";
import { OrderInfoItem } from "@/api/types/OrderInfoGetListResponse.ts";
import {
  ConstructionStatus,
  DesignStatus,
  MeasureStatus,
} from "@/types/OrderStatus.ts";

const tablePageSizeOptions = ["5", "10", "20", "50", "100"];

const tableDataSource = ref<OrderInfoItem[]>([]);
const tableColumns = ref(tableColumnsData);
const tableLoading = ref<boolean>(true);
const tablePagination = ref({
  pageSize: 10,
  current: 1,
});
const currentDataCount = ref<number>(0);
const tableSearchBox = ref({
  text: "",
  key: "",
  pressSearch: (confirm: any, column: any, text: string) => {
    confirm();
    tableSearchBox.value.key = column.key;
    tableSearchBox.value.text = text;
  },
  pressReset: (clearFilters: any) => {
    clearFilters({ confirm: true });
    tableSearchBox.value.text = "";
  },
});
const orderType = ref<OrderType>(OrderType.Measure);

const getOrderStatusName = (
  orderType: OrderType,
  orderStatus: MeasureStatus | DesignStatus | ConstructionStatus
) => {
  switch (orderType) {
    case OrderType.Measure: {
      const status = orderStatus as MeasureStatus;
      switch (status) {
        case MeasureStatus.VerifyPhone:
          return "未註冊帳號";
        case MeasureStatus.WaitingSurveyor:
          return "訂單刊登中";
        case MeasureStatus.SurveyorComing:
          return "已接單等待丈量";
        case MeasureStatus.WaitingUpload:
          return "已到府丈量";
        case MeasureStatus.SurveyDone:
          return "訂單已完成";
        case MeasureStatus.RefillTime:
          return "重填時間";
        default:
          return "未知狀態";
      }
    }
    case OrderType.Design: {
      const status = orderStatus as DesignStatus;
      switch (status) {
        case DesignStatus.Comparing:
          return "訂單刊登中";
        case DesignStatus.Contracting:
          return "合約簽署中";
        case DesignStatus.Working:
          return "雙方工作中";
        case DesignStatus.Completed:
          return "訂單已完成";
        default:
          return "未知狀態";
      }
    }
    case OrderType.Construction: {
      const status = orderStatus as ConstructionStatus;
      switch (status) {
        case ConstructionStatus.Comparing:
          return "訂單刊登中";
        case ConstructionStatus.Quoting:
          return "詳細報價中";
        case ConstructionStatus.Contracting:
          return "合約簽署中";
        case ConstructionStatus.Working:
          return "雙方工作中";
        case ConstructionStatus.Completed:
          return "訂單已完成";
        default:
          return "未知狀態";
      }
    }
  }
};

const indexOperation = (action: string, record: OrderInfoItem) => {
  switch (action) {
    default:
      notifyPush({
        type: notifyType.info,
        message: "功能尚未開發",
        description: `需增加功能請洽詢開發人員 (${record.orderId})`,
      });
  }
};

const onChange: TableProps<UserProfileListItem>["onChange"] = (
  _pagination,
  _filters,
  _sorter
) => {};

const refresh = async () => {
  tableLoading.value = true;
  const data = (
    await orderContractApi.info.getList(
      (tablePagination.value.current - 1) * tablePagination.value.pageSize,
      tablePagination.value.pageSize,
      orderType.value
    )
  ).result;
  tableDataSource.value = data.orders;
  currentDataCount.value = data.total;
  tableLoading.value = false;
};

onMounted(async () => {
  await refresh();
});
</script>
<style>
.header-icon {
  color: gray;
  margin-right: 2px;
}
</style>
