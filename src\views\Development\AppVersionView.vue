<template>
  <div style="display: flex; flex-wrap: wrap">
    <h2>版本設置：</h2>
    <p style="color: red; margin: 4px">
      ！注意！非開發人員請勿操作此設置！亂操作可能導致所有APP無法運行！注意！
    </p>
  </div>
  <a-descriptions :column="2" bordered size="small" style="margin-bottom: 16px">
    <a-descriptions-item label="Android 客戶端">
      <a-input v-model:value="updateInput.androidCustomerAppVersion" />
    </a-descriptions-item>
    <a-descriptions-item label="Android 設計師端">
      <a-input v-model:value="updateInput.androidDesignerAppVersion" />
    </a-descriptions-item>
    <a-descriptions-item label="iOS 客戶端">
      <a-input v-model:value="updateInput.appleCustomerAppVersion" />
    </a-descriptions-item>
    <a-descriptions-item label="iOS 設計師端">
      <a-input v-model:value="updateInput.appleDesignerAppVersion" />
    </a-descriptions-item>
  </a-descriptions>

  <div
    style="
      display: flex;
      align-items: center;
      margin-top: 24px;
      margin-bottom: 16px;
    "
  >
    <h2 style="margin: 0; margin-right: 16px">客戶端Step設置：</h2>
    <span style="margin-right: 8px; font-size: 14px">一鍵控制：</span>
    <a-switch
      v-model:checked="customerStepsAllOn"
      @change="onCustomerStepsAllToggle"
      checked-children="全開"
      un-checked-children="全關"
    />
  </div>
  <a-descriptions :column="2" bordered size="small" style="margin-bottom: 16px">
    <a-descriptions-item label="客戶端Step1">
      <a-switch
        v-model:checked="updateInput.customerStep1"
        @change="updateCustomerStepsAllStatus"
        checked-children="開啟"
        un-checked-children="關閉"
      />
    </a-descriptions-item>
    <a-descriptions-item label="客戶端Step2">
      <a-switch
        v-model:checked="updateInput.customerStep2"
        @change="updateCustomerStepsAllStatus"
        checked-children="開啟"
        un-checked-children="關閉"
      />
    </a-descriptions-item>
    <a-descriptions-item label="客戶端Step3">
      <a-switch
        v-model:checked="updateInput.customerStep3"
        @change="updateCustomerStepsAllStatus"
        checked-children="開啟"
        un-checked-children="關閉"
      />
    </a-descriptions-item>
    <a-descriptions-item label="客戶端Step4">
      <a-switch
        v-model:checked="updateInput.customerStep4"
        @change="updateCustomerStepsAllStatus"
        checked-children="開啟"
        un-checked-children="關閉"
      />
    </a-descriptions-item>
  </a-descriptions>

  <div
    style="
      display: flex;
      align-items: center;
      margin-top: 24px;
      margin-bottom: 16px;
    "
  >
    <h2 style="margin: 0; margin-right: 16px">設計師端Step設置：</h2>
    <span style="margin-right: 8px; font-size: 14px">一鍵控制：</span>
    <a-switch
      v-model:checked="designerStepsAllOn"
      @change="onDesignerStepsAllToggle"
      checked-children="全開"
      un-checked-children="全關"
    />
  </div>
  <a-descriptions :column="2" bordered size="small" style="margin-bottom: 32px">
    <a-descriptions-item label="設計師端Step1">
      <a-switch
        v-model:checked="updateInput.designerStep1"
        @change="updateDesignerStepsAllStatus"
        checked-children="開啟"
        un-checked-children="關閉"
      />
    </a-descriptions-item>
    <a-descriptions-item label="設計師端Step2">
      <a-switch
        v-model:checked="updateInput.designerStep2"
        @change="updateDesignerStepsAllStatus"
        checked-children="開啟"
        un-checked-children="關閉"
      />
    </a-descriptions-item>
    <a-descriptions-item label="設計師端Step3">
      <a-switch
        v-model:checked="updateInput.designerStep3"
        @change="updateDesignerStepsAllStatus"
        checked-children="開啟"
        un-checked-children="關閉"
      />
    </a-descriptions-item>
  </a-descriptions>

  <!-- 統一更新按鈕區域 -->
  <div
    style="
      text-align: center;
      margin-bottom: 32px;
      padding: 20px;
      background-color: #f5f5f5;
      border-radius: 8px;
      border: 2px dashed #d9d9d9;
    "
  >
    <p
      style="
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: bold;
        color: #1890ff;
      "
    >
      確認所有設置無誤後，點擊下方按鈕進行更新
    </p>
    <a-button
      size="large"
      type="primary"
      @click="update()"
      style="font-size: 16px; height: 48px; padding: 0 32px"
    >
      確定設置並更新全部配置
    </a-button>
    <p style="color: red; margin-top: 12px; margin-bottom: 0">
      ！注意！非開發人員請勿操作此設置！亂操作可能導致所有APP無法運行！注意！
    </p>
  </div>

  <h2 style="margin-top: 32px">歷史記錄：(僅顯示最近30筆記錄)</h2>
  <a-list :data-source="docs" bordered item-layout="horizontal" size="small">
    <template #renderItem="{ item, index }">
      <a-list-item>
        <div>
          <div style="margin-bottom: 8px">
            <b v-if="index === 0" style="color: forestgreen">
              {{ new Date(item.createTime).toLocaleString() }}
            </b>
            <b v-else>{{ new Date(item.createTime).toLocaleString() }}</b>
          </div>
          <div style="margin-bottom: 8px">
            <a-tag>Android 客戶端：{{ item.androidCustomerAppVersion }}</a-tag>
            <a-tag
              >Android 設計師端：{{ item.androidDesignerAppVersion }}</a-tag
            >
            <a-tag>iOS 客戶端：{{ item.appleCustomerAppVersion }}</a-tag>
            <a-tag>iOS 設計師端：{{ item.appleDesignerAppVersion }}</a-tag>
          </div>
          <div style="margin-bottom: 8px">
            <span style="font-weight: bold; margin-right: 8px">客戶端：</span>
            <a-tag :color="item.customerStep1 ? 'green' : 'red'">
              Step1：{{ item.customerStep1 ? "開啟" : "關閉" }}
            </a-tag>
            <a-tag :color="item.customerStep2 ? 'green' : 'red'">
              Step2：{{ item.customerStep2 ? "開啟" : "關閉" }}
            </a-tag>
            <a-tag :color="item.customerStep3 ? 'green' : 'red'">
              Step3：{{ item.customerStep3 ? "開啟" : "關閉" }}
            </a-tag>
            <a-tag :color="item.customerStep4 ? 'green' : 'red'">
              Step4：{{ item.customerStep4 ? "開啟" : "關閉" }}
            </a-tag>
          </div>
          <div>
            <span style="font-weight: bold; margin-right: 8px">設計師端：</span>
            <a-tag :color="item.designerStep1 ? 'green' : 'red'">
              Step1：{{ item.designerStep1 ? "開啟" : "關閉" }}
            </a-tag>
            <a-tag :color="item.designerStep2 ? 'green' : 'red'">
              Step2：{{ item.designerStep2 ? "開啟" : "關閉" }}
            </a-tag>
            <a-tag :color="item.designerStep3 ? 'green' : 'red'">
              Step3：{{ item.designerStep3 ? "開啟" : "關閉" }}
            </a-tag>
          </div>
        </div>
      </a-list-item>
    </template>
  </a-list>
</template>
<script lang="ts" setup>
import { DevAppVersionGetHistoryItem } from "@/api/types/DevAppVersionGetHistoryResponse.ts";
import { createVNode, ref } from "vue";
import developmentApi from "@/api/developmentApi.ts";
import { notifyPushError } from "@/services/notify.ts";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { Modal } from "ant-design-vue";

const docs = ref<DevAppVersionGetHistoryItem[]>([]);
const updateInput = ref<{
  androidCustomerAppVersion: string;
  androidDesignerAppVersion: string;
  appleCustomerAppVersion: string;
  appleDesignerAppVersion: string;
  customerStep1: boolean;
  customerStep2: boolean;
  customerStep3: boolean;
  customerStep4: boolean;
  designerStep1: boolean;
  designerStep2: boolean;
  designerStep3: boolean;
}>({
  androidCustomerAppVersion: "",
  androidDesignerAppVersion: "",
  appleCustomerAppVersion: "",
  appleDesignerAppVersion: "",
  customerStep1: false,
  customerStep2: false,
  customerStep3: false,
  customerStep4: false,
  designerStep1: false,
  designerStep2: false,
  designerStep3: false,
});

// 客戶端一鍵控制開關狀態
const customerStepsAllOn = ref(false);

// 設計師端一鍵控制開關狀態
const designerStepsAllOn = ref(false);

// 客戶端一鍵控制開關變更處理
const onCustomerStepsAllToggle = (checked: boolean) => {
  updateInput.value.customerStep1 = checked;
  updateInput.value.customerStep2 = checked;
  updateInput.value.customerStep3 = checked;
  updateInput.value.customerStep4 = checked;
};

// 設計師端一鍵控制開關變更處理
const onDesignerStepsAllToggle = (checked: boolean) => {
  updateInput.value.designerStep1 = checked;
  updateInput.value.designerStep2 = checked;
  updateInput.value.designerStep3 = checked;
};

// 更新客戶端一鍵控制開關狀態
const updateCustomerStepsAllStatus = () => {
  const allOn =
    updateInput.value.customerStep1 &&
    updateInput.value.customerStep2 &&
    updateInput.value.customerStep3 &&
    updateInput.value.customerStep4;
  const allOff =
    !updateInput.value.customerStep1 &&
    !updateInput.value.customerStep2 &&
    !updateInput.value.customerStep3 &&
    !updateInput.value.customerStep4;

  if (allOn) {
    customerStepsAllOn.value = true;
  } else if (allOff) {
    customerStepsAllOn.value = false;
  }
  // 如果是部分開啟狀態，保持當前一鍵控制開關狀態不變
};

// 更新設計師端一鍵控制開關狀態
const updateDesignerStepsAllStatus = () => {
  const allOn =
    updateInput.value.designerStep1 &&
    updateInput.value.designerStep2 &&
    updateInput.value.designerStep3;
  const allOff =
    !updateInput.value.designerStep1 &&
    !updateInput.value.designerStep2 &&
    !updateInput.value.designerStep3;

  if (allOn) {
    designerStepsAllOn.value = true;
  } else if (allOff) {
    designerStepsAllOn.value = false;
  }
  // 如果是部分開啟狀態，保持當前一鍵控制開關狀態不變
};

// 獲取歷史記錄並初始化表單數據
developmentApi.appVersion.getHistory().then((res) => {
  if (res.status !== 0) throw notifyPushError();
  updateInput.value = {
    androidCustomerAppVersion: res.result[0].androidCustomerAppVersion,
    androidDesignerAppVersion: res.result[0].androidDesignerAppVersion,
    appleCustomerAppVersion: res.result[0].appleCustomerAppVersion,
    appleDesignerAppVersion: res.result[0].appleDesignerAppVersion,
    customerStep1: res.result[0].customerStep1,
    customerStep2: res.result[0].customerStep2,
    customerStep3: res.result[0].customerStep3,
    customerStep4: res.result[0].customerStep4,
    designerStep1: res.result[0].designerStep1,
    designerStep2: res.result[0].designerStep2,
    designerStep3: res.result[0].designerStep3,
  };
  docs.value = res.result;

  // 初始化一鍵控制開關狀態
  updateCustomerStepsAllStatus();
  updateDesignerStepsAllStatus();
});

const update = async () => {
  Modal.confirm({
    title: "確定設置並更新全部配置",
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode(
      "div",
      { style: "color:red;" },
      "！注意！非開發人員請勿操作此設置！亂操作可能導致所有APP無法運行！注意！"
    ),
    async onOk() {
      const res = await developmentApi.appVersion.insert(
        updateInput.value.androidCustomerAppVersion,
        updateInput.value.androidDesignerAppVersion,
        updateInput.value.appleCustomerAppVersion,
        updateInput.value.appleDesignerAppVersion,
        updateInput.value.customerStep1,
        updateInput.value.customerStep2,
        updateInput.value.customerStep3,
        updateInput.value.customerStep4,
        updateInput.value.designerStep1,
        updateInput.value.designerStep2,
        updateInput.value.designerStep3
      );
      if (res.status !== 0) throw notifyPushError();
      updateInput.value = {
        androidCustomerAppVersion: res.result[0].androidCustomerAppVersion,
        androidDesignerAppVersion: res.result[0].androidDesignerAppVersion,
        appleCustomerAppVersion: res.result[0].appleCustomerAppVersion,
        appleDesignerAppVersion: res.result[0].appleDesignerAppVersion,
        customerStep1: res.result[0].customerStep1,
        customerStep2: res.result[0].customerStep2,
        customerStep3: res.result[0].customerStep3,
        customerStep4: res.result[0].customerStep4,
        designerStep1: res.result[0].designerStep1,
        designerStep2: res.result[0].designerStep2,
        designerStep3: res.result[0].designerStep3,
      };
      docs.value = res.result;

      // 更新一鍵控制開關狀態
      updateCustomerStepsAllStatus();
      updateDesignerStepsAllStatus();
    },
    onCancel() {
      console.log("Cancel");
    },
    class: "test",
  });
};
</script>
<style scoped></style>
