<template>
  <a-spin :spinning="spinning" tip="Loading...">
    <a-card>
      <a-space>
        <h1>待匯款給設計師 ({{ listDataShow.length === 0 ? "尚無資料" : `共${listDataShow.length}筆` }})：</h1>
        <a-checkbox v-if="hasDeleted" v-model:checked="isShowDeleted" style="margin-bottom: 4px">顯示已刪除帳號的匯款項目</a-checkbox>
      </a-space>
      <a-card v-for="item in listDataShow"
              :title="item.title" size="small">
        <div style="padding: 16px">
          <a-space>
            <a-image :src="item.payee.passbookImage" height="200px"/>
            <div>
              <p>匯款金額： {{ item.remittanceAmount }}元</p>
              <p>戶名： {{ item.payee.accountName }}</p>
              <p>行號： {{ item.payee.bankCode }}</p>
              <p>帳號： {{ item.payee.accountNumber }}</p>
              <div v-if="item.isDeleted">
                <p style="color: red; font-size: medium">！此帳號已被使用者自行刪除，無法透過平台進行匯款！</p>
              </div>
              <template v-else>
                <a-button primary @click="verifyHandle(item.remittanceId, item.files.map(e => e.url))">
                  匯款完成
                </a-button>
                <UploadTool v-model:files="item.files" s3-path="picture" style="margin-left: 8px"/>
              </template>
            </div>
          </a-space>
        </div>
      </a-card>
    </a-card>
  </a-spin>
</template>
<script lang="ts" setup>
import {notifyPush, notifyPushError, notifyType} from "@/services/notify.ts";
import {computed, ref} from "vue";
import {
  DesignerRemittanceGetItem,
  DesignerRemittanceGetResponse
} from "@/api/types/DesignerRemittanceGetResponse.ts";
import orderContractApi from "@/api/orderContractApi.ts";
import UploadTool from "@/components/Order/Audit/UploadTool/UploadTool.vue";
import {FileListType} from "@/components/Order/Audit/UploadTool/UploadToolType.ts";

const spinning = ref<boolean>(true);
const listData = ref<listDataType[]>([]);
const hasDeleted = computed(() => listData.value.some(e => e.isDeleted));
const isShowDeleted = ref<boolean>(false); // TODO：暫存已刪除帳號的顯示狀態
const listDataShow = computed(() => isShowDeleted.value ? listData.value : listData.value.filter(e => !e.isDeleted));

refreshList();

async function refreshList(res: DesignerRemittanceGetResponse | null = null) {
  if (!res) res = await orderContractApi.remittancePlatformGet();
  if (!res || res.status !== 0) notifyPushError();
  else {
    listData.value = res.result.map(item => {
      const title = `【Step${item.orderInfo.orderType}】需匯款給${item.payee.accountName}${item.remittanceAmount}元 (匯款ID：${item.remittanceId}、客戶姓名：${item.orderInfo.customerName})`;
      return {
        ...item,
        title,
        files: [],
        uploading: false,
      };
    });
    spinning.value = false;
  }
}

const verifyHandle = async (remittanceId: string, proofImageUrls: string[]) => {
  if (proofImageUrls.length === 0)
    throw notifyPushError("請上傳檔案");
  const response = await orderContractApi.remittancePlatformSet(remittanceId, proofImageUrls);
  if (!response || response.status !== 0) notifyPushError();
  else {
    notifyPush({
      type: notifyType.success,
      message: `操作成功`,
    });
    await refreshList(response);
  }
};

interface listDataType extends DesignerRemittanceGetItem {
  title: string;
  files: FileListType[];
  uploading: boolean;
}
</script>
<style scoped>
</style>
