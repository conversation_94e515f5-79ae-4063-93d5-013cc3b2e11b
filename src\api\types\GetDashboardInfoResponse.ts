import {ResponseBase} from "@/api/types/ResponseBase.ts";

export interface GetDashboardInfoResponse extends ResponseBase {
  result: GetDashboardInfoItem;
}

export interface GetDashboardInfoItem {
  totalCustomerCount: number;
  activeCustomerCount: number;
  totalDesignerCount: number;
  activeDesignerCount: number;
  notifyCount: {
    userAudit: number;
    orderAuditCustomerRemittance: number;
    orderAuditContract: number;
    orderAuditDesignerRemittance: number;
    orderCustomerInvoice: number;
  };
}
