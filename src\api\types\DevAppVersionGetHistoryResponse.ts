import { ResponseBase } from "@/api/types/ResponseBase.ts";

export interface DevAppVersionGetHistoryResponse extends ResponseBase {
  result: DevAppVersionGetHistoryItem[];
}

export interface DevAppVersionGetHistoryItem {
  createTime: string;
  isDeleted: boolean;
  androidCustomerAppVersion: string;
  androidDesignerAppVersion: string;
  appleCustomerAppVersion: string;
  appleDesignerAppVersion: string;
  customerStep1: boolean;
  customerStep2: boolean;
  customerStep3: boolean;
  customerStep4: boolean;
  designerStep1: boolean;
  designerStep2: boolean;
  designerStep3: boolean;
}
