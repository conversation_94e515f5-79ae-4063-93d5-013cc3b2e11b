import { OrderInfoItem } from "@/api/types/OrderInfoGetListResponse.ts";
import { ColumnData } from "@/composables/User/Profile/columnConfig.ts";
import { OrderType } from "@/types/OrderType.ts";
import {
  ConstructionStatus,
  DesignStatus,
  MeasureStatus,
} from "@/types/OrderStatus.ts";
import { ref, computed } from "vue";

export const orderTypeTab = ref<OrderType>(OrderType.Measure);


const columnData: ColumnData<OrderInfoItem>[] = [
  // {
  //   title: "編號", key: "index", width: 80, fixed: true,
  //   directions: "預設由新到舊註冊時間排序"
  // },
  {
    title: "訂單ID",
    key: "orderId",
    width: 100,
    // sorter: (a, b) => a.orderId.localeCompare(b.orderId),
  },
  {
    title: "訂單類型",
    key: "orderType",
    width: 50,
  },
  {
    title: "狀態",
    key: "status",
    width: 80,
    // sorter: (a, b) => {
    //   let aStatus: number;
    //   let bStatus: number;
    //   switch (a.orderType) {
    //     case OrderType.Measure:
    //       aStatus = a.measureItem.status;
    //       break;
    //     case OrderType.Design:
    //       aStatus = a.designItem.status;
    //       break;
    //     case OrderType.Construction:
    //       aStatus = a.constructionItem.status;
    //       break;
    //     default:
    //       aStatus = -1;
    //       break;
    //   }
    //   switch (b.orderType) {
    //     case OrderType.Measure:
    //       bStatus = b.measureItem.status;
    //       break;
    //     case OrderType.Design:
    //       bStatus = b.designItem.status;
    //       break;
    //     case OrderType.Construction:
    //       bStatus = b.constructionItem.status;
    //       break;
    //     default:
    //       bStatus = -1;
    //       break;
    //   }
    //   return aStatus - bStatus;
    // },

    // filter: [
    //       { text: "未註冊帳號", value: MeasureStatus.VerifyPhone },
    //       { text: "訂單刊登中", value: MeasureStatus.WaitingSurveyor },
    //       { text: "已接單等待丈量", value: MeasureStatus.SurveyorComing },
    //       { text: "已到府丈量", value: MeasureStatus.WaitingUpload },
    //       { text: "訂單已完成", value: MeasureStatus.SurveyDone },
    //       { text: "重填時間", value: MeasureStatus.RefillTime },
    //       { text: "訂單刊登中", value: DesignStatus.Comparing },
    //       { text: "詳細報價中", value: ConstructionStatus.Quoting },
    //     ],
    onFilter: (value: MeasureStatus, record) =>
      record.measureItem.status === value,
    // onFilter: (value, record) => {
    //   switch (record.orderType) {
    //     case OrderType.Measure:
    //       return record.measureItem.status === value;
    //     case OrderType.Design:
    //       return record.designItem.status === value;
    //     case OrderType.Construction:
    //       return record.constructionItem.status === value;
    //     default:
    //       return false;
    //   }
    // }
  },
  {
    title: "客戶",
    key: "customerName",
    width: 60,
    search: true,
    // sorter: (a, b) => a.customerName.localeCompare(b.customerName),
    onFilter: (value, record) => record.customerName.includes(value),
  },
  {
    title: "設計師",
    key: "designerName",
    width: 60,
    search: true,
    // sorter: (a, b) => a.designerName.localeCompare(b.designerName),
    onFilter: (value, record) => record.designerName.includes(value),
  },
  {
    title: "建立時間",
    key: "createTime",
    width: 90,
    // sorter: (a, b) => new Date(a.createTime).getTime() - new Date(b.createTime).getTime(),
  },
  {
    title: "刷新時間",
    key: "refreshTime",
    width: 90,
    // sorter: (a, b) => new Date(a.createTime).getTime() - new Date(b.createTime).getTime(),
  },
  {
    title: "地址",
    key: "address",
    width: 200,
    // search: true,
    // sorter: (a, b) => a.address.fullName.localeCompare(b.address.fullName),
    // onFilter: (value, record) => record.address.fullName.includes(value),
  },
];

const statusFilters = computed(() => {
  switch (orderTypeTab.value) {
    case OrderType.Measure:
      return [
        { text: "未註冊帳號", value: MeasureStatus.VerifyPhone },
        { text: "訂單刊登中", value: MeasureStatus.WaitingSurveyor },
        { text: "已接單等待丈量", value: MeasureStatus.SurveyorComing },
        { text: "已到府丈量", value: MeasureStatus.WaitingUpload },
        { text: "訂單已完成", value: MeasureStatus.SurveyDone },
        { text: "重填時間", value: MeasureStatus.RefillTime },
      ];
    case OrderType.Design:
      return [
        { text: "訂單刊登中", value: DesignStatus.Comparing },
      ];
    case OrderType.Construction:
      return [
        { text: "詳細報價中", value: ConstructionStatus.Quoting },
      ];
    default:
      return [];
  }
});


export const tableColumnsData = computed(() =>
columnData.map((obj) => ({
  key: obj.key,
  dataIndex: obj.key,
  title: obj.title,
  directions: obj.directions,
  width: obj.width ?? 64,
  fixed: obj.fixed ? false : (undefined as "left" | undefined),
  align: obj.align ?? "center",
  sorter:
    obj.sorter === true
      ? (a: any, b: any) => a[obj.key] - b[obj.key]
      : obj.sorter ?? undefined,
  filters: obj.key === 'status' ? statusFilters.value : obj.filter,
  onFilter:
    obj.onFilter !== undefined
      ? obj.onFilter
      : obj.filter
      ? (value: any, record: any) => record[obj.key] === value
      : undefined,
  customFilterDropdown: obj.search,
}))
);

