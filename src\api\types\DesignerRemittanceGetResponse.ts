import {ResponseBase} from "@/api/types/ResponseBase.ts";
import {UserType} from "@/types/UserType.ts";
import {OrderType} from "@/types/OrderType.ts";

export interface DesignerRemittanceGetResponse extends ResponseBase {
  result: DesignerRemittanceGetItem[];
}

export interface DesignerRemittanceGetItem {
  remittanceId: string;
  createTime: Date;
  refreshTime: Date;
  payee: {
    accountName: string;
    bankCode: string;
    accountNumber: string;
    passbookImage: string;
  };
  userType: UserType;
  userId: string;
  orderInfo: {
    orderId: string;
    customerName: string;
    orderType: OrderType;
  };
  totalAmount: number;
  platformFee: number;
  remittanceAmount: number;
  isDeleted: boolean;
}
