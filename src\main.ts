import {createApp} from 'vue'
import Antd from 'ant-design-vue';
import App from './App.vue'
import router from './router'
import 'ant-design-vue/dist/reset.css';
import {createPinia} from "pinia";
import {requestNotificationPermission} from "./services/notification.ts";
import {useCookies} from "vue3-cookies";
import {initFirebaseApp} from "./services/firebase.ts";

const {cookies} = useCookies();

// 請求網頁通知權限，僅限第一次開啟網頁時顯示
if (!cookies.get("requestNotificationPermission")) {
  cookies.set("requestNotificationPermission", new Date().getTime().toString());
  requestNotificationPermission().then(() => {
    cookies.set("requestNotificationPermission", new Date().getTime().toString());
  });
}

// 初始化 Firebase
try {
  initFirebaseApp();
} catch (e) {
  console.error("Firebase 初始化失敗", e);
}

createApp(App)
  .use(router)
  .use(Antd)
  .use(createPinia())
  .mount('#app');
