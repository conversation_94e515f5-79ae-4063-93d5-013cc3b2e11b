import { Gender } from "@/types/Gender.ts";
import {
  DeviceTypeEnum,
  UserProfileListItem,
} from "@/api/types/UserProfileListResponse.ts";
import { UserType } from "@/types/UserType.ts";
import { VerifyStatus } from "@/types/VerifyStatus.ts";

const columnData: ColumnData<UserProfileListItem>[] = [
  {
    title: "編號",
    key: "index",
    width: 80,
    fixed: true,
    sorter: (a, b) => a.index - b.index,
    directions: "預設由新到舊註冊時間排序",
  },
  { title: "照片", key: "avatar", width: 60, fixed: true },
  {
    title: "名稱",
    key: "username",
    fixed: true,
    width: 128,
    search: true,
    onFilter: (value, record) => record.username.includes(value),
  },
  {
    title: "類型",
    key: "userType",
    width: 92,
    directions: "設計師欄位可修改工作資訊",
    filter: [
      { text: "客戶", value: UserType.Customer },
      { text: "設計師", value: UserType.Designer },
    ],
  },
  {
    title: "電話",
    key: "phone",
    width: 100,
    search: true,
    onFilter: (value, record) => record.phone.includes(value),
  },
  {
    title: "信箱",
    key: "email",
    width: 168,
    search: true,
    onFilter: (value, record) => record.email.address.includes(value),
  },
  {
    title: "姓別",
    key: "gender",
    width: 80,
    filter: [
      { text: "未填", value: Gender.Unknown },
      { text: "男性", value: Gender.Male },
      { text: "女性", value: Gender.Female },
      { text: "保密", value: Gender.Private },
    ],
  },
  {
    title: "裝置",
    key: "deviceInfo",
    width: 90,
    filter: [
      { text: "未知", value: DeviceTypeEnum.Unknown },
      { text: "Android", value: DeviceTypeEnum.Android },
      { text: "iOS", value: DeviceTypeEnum.IOS },
      { text: "Web", value: DeviceTypeEnum.Web },
    ],
    onFilter: (value: DeviceTypeEnum, record) =>
      record.deviceInfo.deviceType === value,
  },
  {
    title: "首次註冊",
    key: "createTime",
    width: 90,
    sorter: (a, b) =>
      new Date(a.createTime).getTime() - new Date(b.createTime).getTime(),
  },
  {
    title: "上次使用",
    key: "refreshTime",
    width: 90,
    sorter: (a, b) =>
      new Date(a.refreshTime).getTime() - new Date(b.refreshTime).getTime(),
  },
  {
    title: "身分證",
    key: "taiwanId",
    width: 64,
    filter: [
      { text: "未填寫", value: VerifyStatus.Init },
      { text: "審核中", value: VerifyStatus.Verifying },
      { text: "已通過", value: VerifyStatus.Verified },
      { text: "審核失敗", value: VerifyStatus.VerifyFail },
    ],
    onFilter: (value: VerifyStatus, record) =>
      record.taiwanId.verifyStatus === value,
  },
  {
    title: "公司",
    key: "company",
    width: 64,
    filter: [
      { text: "無", value: null },
      { text: "未填寫", value: VerifyStatus.Init },
      { text: "審核中", value: VerifyStatus.Verifying },
      { text: "已通過", value: VerifyStatus.Verified },
      { text: "審核失敗", value: VerifyStatus.VerifyFail },
    ],
    onFilter: (value: VerifyStatus | null, record) =>
      (record.company?.verifyStatus ?? null) === value,
  },
  {
    title: "戶頭",
    key: "bankAccount",
    width: 64,
    filter: [
      { text: "無", value: null },
      { text: "未設定", value: false },
      { text: "已設定", value: true },
    ],
    onFilter: (value: boolean | null, record) =>
      (record.bankAccount?.isSet ?? null) === value,
  },
  {
    title: "接/發單",
    key: "orderStatus",
    width: 100,
    directions: "根據用戶類型和審核狀態顯示接單或發單狀態",
    filter: [
      { text: "未通過審核", value: "not_verified" },
      { text: "可發單", value: "can_place_order" },
      { text: "不可發單", value: "cannot_place_order" },
      { text: "可接單", value: "can_take_order" },
      { text: "不可接單", value: "cannot_take_order" },
    ],
    onFilter: (value: string, record) => {
      if (record.verifyStatus !== VerifyStatus.Verified) {
        return value === "not_verified";
      }

      if (record.userType === UserType.Customer) {
        return record.isOrderEnabled
          ? value === "can_place_order"
          : value === "cannot_place_order";
      } else if (record.userType === UserType.Designer) {
        return record.isOrderEnabled
          ? value === "can_take_order"
          : value === "cannot_take_order";
      }

      return false;
    },
  },
];

export const tableColumnsData = columnData.map((obj) => ({
  key: obj.key,
  dataIndex: obj.key,
  title: obj.title,
  directions: obj.directions,
  width: obj.width ?? 64,
  fixed: obj.fixed ? false : (undefined as "left" | undefined),
  align: obj.align ?? "center",
  sorter:
    obj.sorter === true
      ? (a: any, b: any) => a[obj.key] - b[obj.key]
      : obj.sorter ?? undefined,
  filters: obj.filter,
  onFilter:
    obj.onFilter !== undefined
      ? obj.onFilter
      : obj.filter
      ? (value: any, record: any) => record[obj.key] === value
      : undefined,
  customFilterDropdown: obj.search,
}));

export interface ColumnData<T> {
  key: string;
  title: string;
  directions?: string;
  width?: number;
  fixed?: boolean;
  align?: "center" | "left" | "right";
  sorter?: true | ((a: T, b: T) => number);
  filter?: { text: string; value: any }[];
  onFilter?: (value: any, record: T) => boolean;
  search?: boolean;
}
