import {ResponseBase} from "@/api/types/ResponseBase.ts";
import {OrderType} from "@/types/OrderType.ts";

export interface CustomerRemittanceGetResponse extends ResponseBase {
  result: CustomerRemittanceGetItem[];
}

export interface CustomerRemittanceGetItem {
  remittanceId: string;
  remittanceTime: string;
  customerId: string;
  customerName: string;
  orderType: OrderType;
  orderId: string;
  amount: number;
  proofImageUrls: string[];
}
