import { postApiByToken } from "@/api/baseApi";
import { DevAppVersionGetHistoryResponse } from "@/api/types/DevAppVersionGetHistoryResponse.ts";
import { DevPlatformBankAccountGetResponse } from "@/api/types/DevPlatformBankAccountGetResponse.ts";
import { DevAdministratorGetResponse } from "@/api/types/DevAdministratorGetResponse.ts";
import { ContractContentGetResponse } from "@/api/types/ContractContentGetResponse.ts";
import { ContractType } from "@/types/ContractType.ts";

export default {
  appVersion: {
    getHistory: async () =>
      await postApiByToken<DevAppVersionGetHistoryResponse>(
        "Ms/Dev/AppVersion/GetHistory"
      ),

    insert: async (
      androidCustomerAppVersion: string,
      androidDesignerAppVersion: string,
      appleCustomerAppVersion: string,
      appleDesignerAppVersion: string,
      customerStep1: boolean,
      customerStep2: boolean,
      customerStep3: boolean,
      customerStep4: boolean,
      designerStep1: boolean,
      designerStep2: boolean,
      designerStep3: boolean
    ) =>
      await postApiByToken<DevAppVersionGetHistoryResponse>(
        "Ms/Dev/AppVersion/Insert",
        {
          androidCustomerAppVersion,
          androidDesignerAppVersion,
          appleCustomerAppVersion,
          appleDesignerAppVersion,
          customerStep1,
          customerStep2,
          customerStep3,
          customerStep4,
          designerStep1,
          designerStep2,
          designerStep3,
        }
      ),
  },
  platformBankAccount: {
    get: async () =>
      await postApiByToken<DevPlatformBankAccountGetResponse>(
        "Ms/Dev/PlatformBankAccount/Get"
      ),

    set: async (
      accountName: string,
      bankCode: string,
      accountNumber: string,
      passbookImage: string
    ) =>
      await postApiByToken<DevPlatformBankAccountGetResponse>(
        "Ms/Dev/PlatformBankAccountDocument/Set",
        {
          accountName,
          bankCode,
          accountNumber,
          passbookImage,
        }
      ),
  },
  contractContent: {
    get: async () =>
      await postApiByToken<ContractContentGetResponse>(
        'Ms/Dev/ContractContent/Get'),

    set: async (
      contentHtml: string,
      version: number,
      type: ContractType,
    ) =>
      await postApiByToken<ContractContentGetResponse>(
        'Ms/Dev/ContractContent/Set', {
          contentHtml,
          version,
          type,
        }),
  },
  Administrator: {
    get: async () =>
      await postApiByToken<DevAdministratorGetResponse>(
        "Ms/Dev/Administrator/Get"
      ),

    set: async (userId: string, set: boolean, description: string) =>
      await postApiByToken<DevAdministratorGetResponse>(
        "Ms/Dev/Administrator/Set",
        {
          userId,
          set,
          description,
        }
      ),
  },
};
