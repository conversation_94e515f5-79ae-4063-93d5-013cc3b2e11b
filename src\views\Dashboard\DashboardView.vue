<template>
  <div style="width: 100%; display: flex; flex-wrap: wrap;">
    <div class="card">
      <a-card>
        <div style="display: flex; justify-content: space-around; flex-wrap: wrap;">
          <a-statistic :value="loading ? '---' : totalCustomerCount" style="margin: 0 30px"
                       title="Total Customers"/>
          <a-statistic :value="loading ? '---' : activeCustomerCount" style="margin: 0 30px"
                       title="Active Customers"/>
          <a-statistic :value="loading ? '---' : totalDesignerCount" style="margin: 0 30px"
                       title="Total Designers"/>
          <a-statistic :value="loading ? '---' : activeDesignerCount" style="margin: 0 30px"
                       title="Active Designers"/>
        </div>
        <p style="text-align: center; color: #ccc; margin: 8px 0 0 0">
          ( Active 的定義為 3 天內有活動 )
        </p>
      </a-card>
      <a-card style="margin-top: 8px">
        <h3 style="margin: 0 8px 8px">常用頁面：</h3>
        <div style="display: flex; flex-wrap: wrap;">
          <div v-for="item in pendingEventShow" style="padding: 6px 10px">
            <a-badge :count="item.count">
              <a-button style="width: 100%" @click="pendingEventClick(item.href)">
                {{ item.title }}
              </a-button>
            </a-badge>
          </div>
        </div>
      </a-card>
    </div>
    <a-card class="card">
      <div style="display: flex; justify-content: space-between;">
        <h3 style="margin: 0 8px 8px">即時事件 (僅顯示本地最新5筆)：</h3>
        <a-button size="small" type="primary" @click="clearNotification">清空</a-button>
      </div>
      <a-list :data-source="notificationQueue" bordered item-layout="horizontal">
        <template #renderItem="{ item }">
          <a-list-item>
            <a-list-item-meta>
              <template #title>
                <a @click="pendingEventClick(item.link)">{{ item.title }}</a>
                <div style="float: right">
                  {{ item.createdAt.toLocaleString() }}
                </div>
              </template>
              <template #description>
                {{ item.body }}
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </a-card>
  </div>
</template>
<script lang="ts" setup>
import {computed, ref} from "vue";
import router from "@/router";
import dashboardApi from "@/api/dashboardApi.ts";
import {notifyPushError} from "@/services/notify.ts";
import {clearNotificationQueue, getNotificationQueue, QueueType} from "@/services/indexedDb.ts";

const notificationQueue = ref<QueueType[]>([]);
const loading = ref<boolean>(true);
const totalCustomerCount = ref<number>(0);
const activeCustomerCount = ref<number>(0);
const totalDesignerCount = ref<number>(0);
const activeDesignerCount = ref<number>(0);

setInterval(async () => {
  // console.log('update notification queue.');
  const result = await getNotificationQueue();
  notificationQueue.value = result
      .slice(result.length - 5, result.length)
      .reverse();
}, 1000);

const clearNotification = async () => {
  notificationQueue.value = [];
  await clearNotificationQueue();
};

const pendingEventShow = computed(() => Object.keys(pendingEventList.value)
    .map(key => pendingEventList.value[key])
    .sort((a, b) => b.count - a.count));

dashboardApi.getDashboardInfo().then(res => {
  if (!res || res.status !== 0) notifyPushError();
  else {
    const refItem = pendingEventList.value;
    const resItem = res.result.notifyCount;
    refItem.userAudit.count = resItem.userAudit;
    refItem.orderAuditCustomerRemittance.count = resItem.orderAuditCustomerRemittance;
    refItem.orderAuditContract.count = resItem.orderAuditContract;
    refItem.orderAuditDesignerRemittance.count = resItem.orderAuditDesignerRemittance;
    refItem.orderCustomerInvoice.count = resItem.orderCustomerInvoice;
    totalCustomerCount.value = res.result.totalCustomerCount;
    activeCustomerCount.value = res.result.activeCustomerCount;
    totalDesignerCount.value = res.result.totalDesignerCount;
    activeDesignerCount.value = res.result.activeDesignerCount;
    loading.value = false;
  }
});

const pendingEventList = ref<{ [key: string]: PendingEvent }>({
  userAudit: {
    href: 'user/audit?type=all',
    title: '用戶審核列表',
    count: 0,
  },
  orderAuditCustomerRemittance: {
    href: 'order/audit?type=customerRemittance',
    title: '訂單客戶匯款審核',
    count: 0,
  },
  orderAuditContract: {
    href: 'order/audit?type=contract',
    title: '訂單合約審核',
    count: 0,
  },
  orderAuditDesignerRemittance: {
    href: 'order/audit?type=designerRemittance',
    title: '訂單待匯款給設計師',
    count: 0,
  },
  orderCustomerInvoice: {
    href: 'order/audit?type=customerInvoice',
    title: '待開立發票給客戶',
    count: 0,
  },
});

const pendingEventClick = (href: string) => {
  router.push(href);
  console.log('pendingEventClick');
};

interface PendingEvent {
  href: string;
  title: string;
  count: number;
}
</script>
<style scoped>
.card {
  width: calc(50% - 8px);
  margin: 4px;
}

@media (max-width: 768px) {
  .card {
    width: 100%;
    margin: 4px;
  }
}
</style>
