import {openDB} from 'idb';

const dbName = 'homeeasy-database';
const tableName = 'notification';

const dbPromise = openDB(dbName, 1, {
  upgrade(db) {
    db.createObjectStore(tableName);
  },
});

export async function setVariable(name: string, value: any) {
  const db = await dbPromise;
  await db.put(tableName, value, name);
}

export async function getVariable(name: string) {
  const db = await dbPromise;
  try {
    return await db.get(tableName, name);
  } catch {
    return null;
  }
}

export async function pushNotificationQueue(item: QueueType) {
  const queue: QueueType[] = await getVariable('queue') || [];
  queue.push(item);
  await setVariable('queue', queue);
}

export async function getNotificationQueue(): Promise<QueueType[]> {
  return await getVariable('queue') || [];
}

export async function clearNotificationQueue() {
  await setVariable('queue', []);
}

export interface QueueType {
  createdAt: Date;
  title: string;
  body: string;
  link: string;
}
