import {defineStore} from "pinia";
import {useCookies} from "vue3-cookies";

const {cookies} = useCookies();

export const userStatusStore = defineStore('userStatus', {
  state: () => {
    const userId = cookies.get('userId') ?? null;
    const token = cookies.get('token') ?? null;
    return {
      userId: userId as string | null,
      token: token as string | null,
    }
  },
  getters: {
    isLoggedIn: (state): boolean => state.token !== null,
  },
  actions: {
    async setLogIn(userId: string, token: string, remember: boolean) {
      this.userId = userId;
      this.token = token;
      if (remember) {
        cookies.set('userId', userId, '1y');
        cookies.set('token', token, '1y');
      }
    },
    setLogOut() {
      this.token = null;
      this.userId = null;
      cookies.remove('token');
      cookies.remove('userId');
    },
  },
});
